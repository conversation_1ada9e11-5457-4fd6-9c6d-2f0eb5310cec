import { isAdmin } from '~/composables/info'

interface MobileNavigationState {
  activeTab: string
  isMenuOpen: boolean
  isSearchOpen: boolean
  scrollDirection: 'up' | 'down'
  showTopNav: boolean
  isCompact: boolean
  lastScrollY: number
  scrollVelocity: number
}

interface NavigationTab {
  id: string
  label: string
  icon: string
  activeIcon?: string
  route: string
  badge?: number
  disabled?: boolean
}

interface HeaderAction {
  id: string
  icon: string
  label: string
  handler: () => void
  class?: string
  disabled?: boolean
}

interface MenuItem {
  id: string
  label: string
  icon: string
  action: string
  class?: string
  hasSubmenu?: boolean
  badge?: number
  disabled?: boolean
}

interface MenuSection {
  title: string
  items: MenuItem[]
}

export const useMobileNavigation = () => {
  // Reactive state
  const state = reactive<MobileNavigationState>({
    activeTab: 'dashboard', // Default to new welcome dashboard
    isMenuOpen: false,
    isSearchOpen: false,
    scrollDirection: 'down',
    showTopNav: true,
    isCompact: false,
    lastScrollY: 0,
    scrollVelocity: 0
  })

  // Router and route
  const router = useRouter()
  const route = useRoute()

  // Scroll handling variables
  const scrollThreshold = 10
  const compactThreshold = 100
  const hideThreshold = 150
  let scrollTimer: NodeJS.Timeout | null = null
  let velocityTimer: NodeJS.Timeout | null = null

  // Get user state for reactivity
  const currentUser = useState('currentUser', () => ({}))
  const currentClient = useState('currentClient', () => ({}))
  const currentSpace = useState('currentSpace', () => ({}))

  // Dynamic navigation tabs based on user role
  const defaultNavigationTabs = computed<NavigationTab[]>(() => {
    const tabs: NavigationTab[] = [
      {
        id: 'dashboard',
        label: 'Home',
        icon: 'mdi:view-dashboard-outline',
        activeIcon: 'mdi:view-dashboard',
        route: '/c/welcome'
      },
      {
        id: 'crm',
        label: 'CRM',
        icon: 'mdi:account-multiple-outline',
        activeIcon: 'mdi:account-multiple',
        route: '/c/crm-dashboard'
      },
      {
        id: 'profile',
        label: 'Profile',
        icon: 'mdi:account-outline',
        activeIcon: 'mdi:account',
        route: '/c/profile-dashboard'
      }
    ]

    // Conditionally add Admin tab for admin users - force reactivity by accessing state values
    if (currentUser.value || currentClient.value || currentSpace.value) {
      if (isAdmin()) {
        tabs.push({
          id: 'admin',
          label: 'Admin',
          icon: 'mdi:shield-crown-outline',
          activeIcon: 'mdi:shield-crown',
          route: '/c/admin'
        })
      }
    }

    return tabs
  })

  // Default menu sections - streamlined to avoid duplication
  const defaultMenuSections: MenuSection[] = [
    {
      title: 'Data Management',
      items: [
        {
          id: 'export',
          label: 'Export My Data',
          icon: 'mdi:download',
          action: 'export-data'
        },
        {
          id: 'import',
          label: 'Import Contacts',
          icon: 'mdi:upload',
          action: 'import-data'
        },
        {
          id: 'backup',
          label: 'Backup & Sync',
          icon: 'mdi:cloud-sync',
          action: 'navigate:/backup'
        }
      ]
    },
    {
      title: 'Advanced',
      items: [
        {
          id: 'all-contacts',
          label: 'All Contacts',
          icon: 'mdi:account-group',
          action: 'navigate:/businesscards'
        },
        {
          id: 'analytics',
          label: 'Analytics Dashboard',
          icon: 'mdi:chart-line',
          action: 'navigate:/c/dashboard'
        },
        {
          id: 'integrations',
          label: 'Integrations',
          icon: 'mdi:puzzle',
          action: 'navigate:/integrations'
        },
        {
          id: 'api',
          label: 'API Access',
          icon: 'mdi:code-braces',
          action: 'navigate:/api'
        }
      ]
    },
    {
      title: 'Support',
      items: [
        {
          id: 'feedback',
          label: 'Send Feedback',
          icon: 'mdi:message-text',
          action: 'open-feedback'
        },
        {
          id: 'contact',
          label: 'Contact Us',
          icon: 'mdi:email',
          action: 'open-contact'
        },
        {
          id: 'about',
          label: 'About',
          icon: 'mdi:information',
          action: 'navigate:/about'
        },
        {
          id: 'privacy',
          label: 'Privacy Policy',
          icon: 'mdi:shield-check',
          action: 'navigate:/privacy'
        },
        {
          id: 'terms',
          label: 'Terms of Service',
          icon: 'mdi:file-document',
          action: 'navigate:/terms'
        }
      ]
    }
  ]

  // Scroll handler with velocity tracking
  const handleScroll = () => {
    const currentScrollY = window.scrollY
    const deltaY = currentScrollY - state.lastScrollY

    // Calculate scroll velocity
    state.scrollVelocity = Math.abs(deltaY)

    // Reset velocity after a delay
    if (velocityTimer) clearTimeout(velocityTimer)
    velocityTimer = setTimeout(() => {
      state.scrollVelocity = 0
    }, 150)

    // Only update if scroll difference is significant
    if (Math.abs(deltaY) < scrollThreshold) {
      return
    }

    // Determine scroll direction
    const newDirection = deltaY > 0 ? 'down' : 'up'
    state.scrollDirection = newDirection

    // Update compact state
    state.isCompact = currentScrollY > compactThreshold

    // Update top nav visibility
    if (currentScrollY < 50) {
      // Always show nav at top of page
      state.showTopNav = true
    } else if (state.scrollVelocity > 20) {
      // Fast scroll - hide/show immediately
      state.showTopNav = newDirection === 'up'
    } else if (Math.abs(deltaY) > hideThreshold) {
      // Slow scroll - only hide/show after significant movement
      state.showTopNav = newDirection === 'up'
    }

    state.lastScrollY = currentScrollY

    // Debounce scroll updates
    if (scrollTimer) clearTimeout(scrollTimer)
    scrollTimer = setTimeout(() => {
      // Additional scroll handling if needed
    }, 100)
  }

  // Navigation methods
  const navigateToTab = (tab: NavigationTab) => {
    if (tab.disabled) return

    state.activeTab = tab.id

    if (tab.id === 'more') {
      openMenu()
    } else {
      router.push(tab.route)
    }
  }

  const openMenu = () => {
    state.isMenuOpen = true
    // Prevent body scroll when menu is open
    document.body.style.overflow = 'hidden'
  }

  const closeMenu = () => {
    state.isMenuOpen = false
    // Restore body scroll
    document.body.style.overflow = ''
  }

  const openSearch = () => {
    state.isSearchOpen = true
    // Prevent body scroll when search is open
    document.body.style.overflow = 'hidden'
  }

  const closeSearch = () => {
    state.isSearchOpen = false
    // Restore body scroll
    document.body.style.overflow = ''
  }

  const handleMenuAction = (item: MenuItem) => {
    const [actionType, actionValue] = item.action.split(':')

    switch (actionType) {
      case 'navigate':
        router.push(actionValue)
        closeMenu()
        break
      case 'logout':
        handleLogout()
        break
      case 'export-data':
        handleExportData()
        break
      case 'import-data':
        handleImportData()
        break
      case 'open-feedback':
        openFeedbackModal()
        break
      case 'open-contact':
        openContactModal()
        break
      default:
        console.log('Unknown menu action:', item.action)
    }
  }

  const handleLogout = async() => {
    try {
      // Implement logout logic
      const { signOut } = useAuth()
      await signOut()
      router.push('/login')
      closeMenu()
    } catch (error) {
      console.error('Logout failed:', error)
    }
  }

  const handleExportData = () => {
    // Implement data export
    console.log('Exporting data...')
    closeMenu()
  }

  const handleImportData = () => {
    // Implement data import
    console.log('Importing data...')
    closeMenu()
  }

  const openFeedbackModal = () => {
    // Implement feedback modal
    console.log('Opening feedback modal...')
    closeMenu()
  }

  const openContactModal = () => {
    // Implement contact modal
    console.log('Opening contact modal...')
    closeMenu()
  }

  // Update active tab based on current route
  const updateActiveTab = () => {
    const currentPath = route.path

    // Handle specific route mappings
    if (currentPath.startsWith('/c/admin')) {
      state.activeTab = 'admin'
      return
    }

    if (currentPath.startsWith('/c/welcome') || currentPath === '/c' || currentPath === '/c/dashboard') {
      state.activeTab = 'dashboard'
      return
    }

    if (currentPath.startsWith('/c/crm-dashboard')) {
      state.activeTab = 'crm'
      return
    }

    if (currentPath.startsWith('/c/calendar') || currentPath.startsWith('/c/profile')) {
      state.activeTab = 'profile'
      return
    }

    // Fallback to route matching
    const matchingTab = defaultNavigationTabs.value.find(tab => {
      if (tab.route === '/') {
        return currentPath === '/'
      }
      return currentPath.startsWith(tab.route)
    })

    if (matchingTab) {
      state.activeTab = matchingTab.id
    } else {
      // Default to dashboard if no match
      state.activeTab = 'dashboard'
    }
  }

  // Handle escape key
  const handleEscapeKey = (event: KeyboardEvent) => {
    if (event.key === 'Escape') {
      if (state.isSearchOpen) {
        closeSearch()
      } else if (state.isMenuOpen) {
        closeMenu()
      }
    }
  }

  // Initialize navigation
  const initMobileNavigation = () => {
    // Set initial active tab
    updateActiveTab()

    // Add scroll listener
    window.addEventListener('scroll', handleScroll, { passive: true })

    // Add escape key listener
    document.addEventListener('keydown', handleEscapeKey)

    // Watch for route changes
    watch(() => route.path, updateActiveTab)
  }

  // Cleanup
  const cleanupMobileNavigation = () => {
    window.removeEventListener('scroll', handleScroll)
    document.removeEventListener('keydown', handleEscapeKey)

    if (scrollTimer) clearTimeout(scrollTimer)
    if (velocityTimer) clearTimeout(velocityTimer)

    // Restore body scroll
    document.body.style.overflow = ''
  }

  // Initialize on mount
  onMounted(() => {
    initMobileNavigation()
  })

  // Cleanup on unmount
  onUnmounted(() => {
    cleanupMobileNavigation()
  })

  return {
    // State
    state: readonly(state),

    // Data
    defaultNavigationTabs,
    defaultMenuSections,

    // Methods
    navigateToTab,
    openMenu,
    closeMenu,
    openSearch,
    closeSearch,
    handleMenuAction,
    updateActiveTab
  }
}
