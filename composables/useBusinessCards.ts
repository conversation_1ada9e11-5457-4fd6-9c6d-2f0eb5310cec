/**
 * Business Cards Composable
 * Manages business card data from Firebase with real-time updates, caching, and CRUD operations
 */

import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  onSnapshot,
  serverTimestamp,
  Timestamp,
  type Unsubscribe,
  type QueryConstraint
} from 'firebase/firestore'
import { useFirebase } from './useFirebase'
import { useAuth } from './useAuth'
import { useOfflineStorage } from './useOfflineStorage'

// Business Card Interface
export interface BusinessCard {
  id: string
  // Basic Information
  name?: string
  first_name?: string
  last_name?: string
  email?: string
  phone?: string
  mobile?: string
  company?: string
  position?: string
  department?: string
  
  // Address Information
  address?: {
    street?: string
    city?: string
    state?: string
    country?: string
    postalCode?: string
  }
  
  // Contact Details
  website?: string
  linkedin?: string
  twitter?: string
  facebook?: string
  instagram?: string
  
  // Additional Information
  notes?: string
  tags?: string[]
  category?: string
  source?: 'scan' | 'manual' | 'import' | 'api' | 'offline' | 'network'
  
  // Location Data
  location?: {
    lat: number
    lng: number
    geohash?: string
  }
  
  // Metadata
  userId: string
  createdAt: Date
  updatedAt: Date
  lastAccessed?: Date
  isFavorite?: boolean
  isArchived?: boolean
  
  // CRM Related
  engagementLevel?: 'cold' | 'warm' | 'hot'
  lastContactDate?: Date
  nextFollowUpDate?: Date
  
  // Business Card Image
  imageUrl?: string
  thumbnailUrl?: string
  
  // Sharing
  isPublic?: boolean
  sharedWith?: string[]
}

// Firebase Business Card (with Timestamps)
interface FirebaseBusinessCard extends Omit<BusinessCard, 'createdAt' | 'updatedAt' | 'lastAccessed' | 'lastContactDate' | 'nextFollowUpDate'> {
  createdAt: Timestamp
  updatedAt: Timestamp
  lastAccessed?: Timestamp
  lastContactDate?: Timestamp
  nextFollowUpDate?: Timestamp
}

export const useBusinessCards = () => {
  // Get Firebase and Auth instances
  const { firestore } = useFirebase()
  const { user, isAuthenticated } = useAuth()
  const { cacheBusinessCard, getCachedBusinessCards, removeCachedBusinessCard } = useOfflineStorage()

  // State
  const businessCards = ref<BusinessCard[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  const searchQuery = ref('')
  const selectedCard = ref<BusinessCard | null>(null)
  
  // Real-time subscription
  let unsubscribe: Unsubscribe | null = null

  // Collection reference
  const getCollectionRef = () => {
    if (!firestore) throw new Error('Firestore not initialized')
    return collection(firestore, 'business-cards')
  }

  // Helper function to convert Firestore timestamp to Date
  const timestampToDate = (timestamp: Timestamp | Date | undefined): Date | undefined => {
    if (!timestamp) return undefined
    if (timestamp instanceof Timestamp) {
      return timestamp.toDate()
    }
    return timestamp
  }

  // Helper function to convert Date to Firestore timestamp
  const dateToTimestamp = (date: Date | undefined): Timestamp | undefined => {
    if (!date) return undefined
    return Timestamp.fromDate(date)
  }

  // Convert Firebase document to BusinessCard
  const convertFirebaseDoc = (id: string, data: FirebaseBusinessCard): BusinessCard => {
    return {
      ...data,
      id,
      createdAt: timestampToDate(data.createdAt) || new Date(),
      updatedAt: timestampToDate(data.updatedAt) || new Date(),
      lastAccessed: timestampToDate(data.lastAccessed),
      lastContactDate: timestampToDate(data.lastContactDate),
      nextFollowUpDate: timestampToDate(data.nextFollowUpDate)
    }
  }

  // Computed properties
  const sortedBusinessCards = computed(() => {
    return [...businessCards.value].sort((a, b) => {
      // Sort by last accessed, then by name
      if (a.lastAccessed && b.lastAccessed) {
        return b.lastAccessed.getTime() - a.lastAccessed.getTime()
      }
      const nameA = a.name || `${a.first_name} ${a.last_name}` || ''
      const nameB = b.name || `${b.first_name} ${b.last_name}` || ''
      return nameA.localeCompare(nameB)
    })
  })

  const filteredBusinessCards = computed(() => {
    if (!searchQuery.value) return sortedBusinessCards.value
    
    const query = searchQuery.value.toLowerCase()
    return sortedBusinessCards.value.filter(card => {
      const name = card.name || `${card.first_name} ${card.last_name}` || ''
      const company = card.company || ''
      const email = card.email || ''
      const tags = card.tags?.join(' ') || ''
      
      return (
        name.toLowerCase().includes(query) ||
        company.toLowerCase().includes(query) ||
        email.toLowerCase().includes(query) ||
        tags.toLowerCase().includes(query)
      )
    })
  })

  const favoriteCards = computed(() => 
    businessCards.value.filter(card => card.isFavorite && !card.isArchived)
  )

  const archivedCards = computed(() => 
    businessCards.value.filter(card => card.isArchived)
  )

  // Subscribe to real-time updates
  const subscribeToBusinessCards = () => {
    if (!firestore || !user.value?.uid) return

    try {
      const collectionRef = getCollectionRef()
      const constraints: QueryConstraint[] = [
        where('userId', '==', user.value.uid),
        orderBy('updatedAt', 'desc')
      ]
      
      const q = query(collectionRef, ...constraints)
      
      unsubscribe = onSnapshot(q, (snapshot) => {
        const cards: BusinessCard[] = []
        snapshot.forEach((doc) => {
          const data = doc.data() as FirebaseBusinessCard
          cards.push(convertFirebaseDoc(doc.id, data))
        })
        businessCards.value = cards
        loading.value = false
      }, (err) => {
        console.error('Error in business cards subscription:', err)
        error.value = err.message
        loading.value = false
      })
    } catch (err: any) {
      console.error('Failed to subscribe to business cards:', err)
      error.value = err.message
      loading.value = false
    }
  }

  // Load business cards (one-time fetch)
  const loadBusinessCards = async () => {
    if (!firestore || !user.value?.uid) return

    loading.value = true
    error.value = null

    try {
      // First, try to get cached cards for offline support
      const cachedCards = await getCachedBusinessCards()
      if (cachedCards.length > 0) {
        businessCards.value = cachedCards
      }

      // Then fetch from Firebase
      const collectionRef = getCollectionRef()
      const constraints: QueryConstraint[] = [
        where('userId', '==', user.value.uid),
        orderBy('updatedAt', 'desc')
      ]
      
      const q = query(collectionRef, ...constraints)
      const snapshot = await getDocs(q)
      
      const cards: BusinessCard[] = []
      snapshot.forEach((doc) => {
        const data = doc.data() as FirebaseBusinessCard
        cards.push(convertFirebaseDoc(doc.id, data))
      })
      
      businessCards.value = cards
    } catch (err: any) {
      console.error('Failed to load business cards:', err)
      error.value = err.message
      
      // Fall back to cached cards if Firebase fails
      const cachedCards = await getCachedBusinessCards()
      if (cachedCards.length > 0) {
        businessCards.value = cachedCards
        error.value = null // Clear error if we have cached data
      }
    } finally {
      loading.value = false
    }
  }

  // Get a single business card
  const getBusinessCard = async (cardId: string): Promise<BusinessCard | null> => {
    if (!firestore) return null

    try {
      const docRef = doc(firestore, 'business-cards', cardId)
      const docSnap = await getDoc(docRef)
      
      if (docSnap.exists()) {
        const data = docSnap.data() as FirebaseBusinessCard
        return convertFirebaseDoc(docSnap.id, data)
      }
      
      return null
    } catch (err: any) {
      console.error('Failed to get business card:', err)
      error.value = err.message
      return null
    }
  }

  // Create a new business card
  const createBusinessCard = async (cardData: Partial<BusinessCard>): Promise<BusinessCard | null> => {
    if (!firestore || !user.value?.uid) {
      error.value = 'Not authenticated'
      return null
    }

    loading.value = true
    error.value = null

    try {
      const collectionRef = getCollectionRef()
      
      // Prepare data for Firebase
      const firebaseData: Omit<FirebaseBusinessCard, 'id'> = {
        ...cardData,
        userId: user.value.uid,
        createdAt: serverTimestamp() as Timestamp,
        updatedAt: serverTimestamp() as Timestamp,
        lastContactDate: dateToTimestamp(cardData.lastContactDate),
        nextFollowUpDate: dateToTimestamp(cardData.nextFollowUpDate),
        source: cardData.source || 'manual'
      }

      const docRef = await addDoc(collectionRef, firebaseData)
      
      // Create the business card object
      const newCard: BusinessCard = {
        ...cardData,
        id: docRef.id,
        userId: user.value.uid,
        createdAt: new Date(),
        updatedAt: new Date(),
        source: cardData.source || 'manual'
      }

      // Cache for offline access if it's a favorite
      if (newCard.isFavorite) {
        await cacheBusinessCard(newCard)
      }

      // If not using real-time updates, add to local array
      if (!unsubscribe) {
        businessCards.value.unshift(newCard)
      }

      return newCard
    } catch (err: any) {
      console.error('Failed to create business card:', err)
      error.value = err.message
      return null
    } finally {
      loading.value = false
    }
  }

  // Update a business card
  const updateBusinessCard = async (cardId: string, updates: Partial<BusinessCard>): Promise<boolean> => {
    if (!firestore) {
      error.value = 'Firestore not initialized'
      return false
    }

    loading.value = true
    error.value = null

    try {
      const docRef = doc(firestore, 'business-cards', cardId)
      
      // Prepare updates for Firebase
      const firebaseUpdates: Partial<FirebaseBusinessCard> = {
        ...updates,
        updatedAt: serverTimestamp() as Timestamp,
        lastContactDate: dateToTimestamp(updates.lastContactDate),
        nextFollowUpDate: dateToTimestamp(updates.nextFollowUpDate)
      }

      // Remove fields that shouldn't be updated
      delete (firebaseUpdates as any).id
      delete (firebaseUpdates as any).userId
      delete (firebaseUpdates as any).createdAt

      await updateDoc(docRef, firebaseUpdates)

      // Update cache if needed
      const card = businessCards.value.find(c => c.id === cardId)
      if (card) {
        const updatedCard = { ...card, ...updates, updatedAt: new Date() }
        
        if (updatedCard.isFavorite) {
          await cacheBusinessCard(updatedCard)
        } else {
          await removeCachedBusinessCard(cardId)
        }

        // Update local state if not using real-time updates
        if (!unsubscribe) {
          const index = businessCards.value.findIndex(c => c.id === cardId)
          if (index > -1) {
            businessCards.value[index] = updatedCard
          }
        }
      }

      return true
    } catch (err: any) {
      console.error('Failed to update business card:', err)
      error.value = err.message
      return false
    } finally {
      loading.value = false
    }
  }

  // Delete a business card
  const deleteBusinessCard = async (cardId: string): Promise<boolean> => {
    if (!firestore) {
      error.value = 'Firestore not initialized'
      return false
    }

    loading.value = true
    error.value = null

    try {
      const docRef = doc(firestore, 'business-cards', cardId)
      await deleteDoc(docRef)

      // Remove from cache
      await removeCachedBusinessCard(cardId)

      // Update local state if not using real-time updates
      if (!unsubscribe) {
        const index = businessCards.value.findIndex(c => c.id === cardId)
        if (index > -1) {
          businessCards.value.splice(index, 1)
        }
      }

      return true
    } catch (err: any) {
      console.error('Failed to delete business card:', err)
      error.value = err.message
      return false
    } finally {
      loading.value = false
    }
  }

  // Toggle favorite status
  const toggleFavorite = async (cardId: string): Promise<boolean> => {
    const card = businessCards.value.find(c => c.id === cardId)
    if (!card) return false

    return updateBusinessCard(cardId, { isFavorite: !card.isFavorite })
  }

  // Archive/Unarchive a card
  const toggleArchive = async (cardId: string): Promise<boolean> => {
    const card = businessCards.value.find(c => c.id === cardId)
    if (!card) return false

    return updateBusinessCard(cardId, { isArchived: !card.isArchived })
  }

  // Update last accessed timestamp
  const updateLastAccessed = async (cardId: string): Promise<void> => {
    if (!firestore) return

    try {
      const docRef = doc(firestore, 'business-cards', cardId)
      await updateDoc(docRef, {
        lastAccessed: serverTimestamp()
      })
    } catch (err) {
      console.error('Failed to update last accessed:', err)
      // Non-critical error, don't show to user
    }
  }

  // Select a card and update last accessed
  const selectBusinessCard = async (card: BusinessCard) => {
    selectedCard.value = card
    await updateLastAccessed(card.id)
  }

  // Initialize composable
  onMounted(() => {
    if (isAuthenticated.value) {
      subscribeToBusinessCards()
    }
  })

  '''  // Watch for auth changes
  watch(isAuthenticated, (newValue) => {
    if (newValue) {
      subscribeToBusinessCards()
    } else {
      if (unsubscribe) {
        unsubscribe()
        unsubscribe = null
      }
      businessCards.value = []
    }
  })

  // Cleanup on unmount
  onUnmounted(() => {
    if (unsubscribe) {
      unsubscribe()
    }
  })

  const loadPublicBusinessCards = async () => {
    if (!firestore) return;

    loading.value = true;
    error.value = null;

    try {
      const collectionRef = getCollectionRef();
      const constraints: QueryConstraint[] = [
        where('isPublic', '==', true),
        where('status', '==', 'active'),
        orderBy('createdAt', 'desc')
      ];
      
      const q = query(collectionRef, ...constraints);
      const snapshot = await getDocs(q);
      
      const cards: BusinessCard[] = [];
      snapshot.forEach((doc) => {
        const data = doc.data() as FirebaseBusinessCard;
        cards.push(convertFirebaseDoc(doc.id, data));
      });
      
      businessCards.value = cards;
    } catch (err: any) {
      console.error('Failed to load public business cards:', err);
      error.value = err.message;
    } finally {
      loading.value = false;
    }
  };

  return {
    // State
    businessCards: computed(() => businessCards.value),
    loading: computed(() => loading.value),
    error: computed(() => error.value),
    searchQuery,
    selectedCard: computed(() => selectedCard.value),
    
    // Computed
    filteredBusinessCards,
    sortedBusinessCards,
    favoriteCards,
    archivedCards,
    
    // Methods
    loadBusinessCards,
    getBusinessCard,
    createBusinessCard,
    updateBusinessCard,
    deleteBusinessCard,
    toggleFavorite,
    toggleArchive,
    selectBusinessCard,
    updateLastAccessed,
    loadPublicBusinessCards
  }
}''