#!/bin/bash

# Enhanced Notification Hook for Claude Code
# Securely handles notifications with context-awareness and cross-platform support
# Fixes security vulnerability and adds agent/workflow integration

# Set error handling
set -o pipefail
trap '' PIPE

# === DEPENDENCY VALIDATION ===
# Check for required dependencies with clear error messages
if ! command -v jq >/dev/null 2>&1; then
    echo "ERROR: jq is required but not installed. Please install jq." >&2
    exit 1
fi

# === READ JSON INPUT ===
# Read JSON input from stdin (REQUIRED for all Claude Code hooks)
if ! JSON_INPUT=$(cat 2>/dev/null); then
    # If we can't read stdin, exit gracefully
    exit 0
fi

# === EXTRACT EVENT DATA ===
# Extract event type and tool name from JSON with error handling
EVENT_TYPE=$(echo "$JSON_INPUT" | jq -r '.event // empty' 2>/dev/null)
TOOL_NAME=$(echo "$JSON_INPUT" | jq -r '.toolName // empty' 2>/dev/null)
HOOK_EVENT_NAME=$(echo "$JSON_INPUT" | jq -r '.hook_event_name // empty' 2>/dev/null)

# Get current project name
CURRENT_DIR=$(pwd)
PROJECT_NAME=$(basename "$CURRENT_DIR")

# If we're in a subdirectory, try to find the project root
if [[ "$CURRENT_DIR" == *"/.claude"* ]] || [[ "$CURRENT_DIR" == */bmad-agent/* ]] || [[ "$CURRENT_DIR" == */layers/* ]] || [[ "$CURRENT_DIR" == */components/* ]]; then
    # Extract project root from path
    PROJECT_ROOT="${CURRENT_DIR%%/.claude*}"
    PROJECT_ROOT="${PROJECT_ROOT%%/bmad-agent*}"
    PROJECT_ROOT="${PROJECT_ROOT%%/layers*}"
    PROJECT_ROOT="${PROJECT_ROOT%%/components*}"
    PROJECT_ROOT="${PROJECT_ROOT%%/app*}"
    PROJECT_NAME=$(basename "$PROJECT_ROOT")
fi

# Get notification parameters from command line args
# Usage: notification-hook.sh "title" "message" "sound"
NOTIFICATION_TITLE="${1:-BMAD-METHOD}"
NOTIFICATION_MESSAGE="${2:-Operation completed}"
NOTIFICATION_SOUND="${3:-Glass}"

# === CONTEXT AWARENESS ===
# Load workflow and agent context if available
load_context() {
    local claude_dir
    # Find .claude directory from current location
    claude_dir="$CURRENT_DIR"
    while [ "$claude_dir" != "/" ]; do
        if [ -d "$claude_dir/.claude" ]; then
            CLAUDE_DIR="$claude_dir/.claude"
            break
        fi
        claude_dir=$(dirname "$claude_dir")
    done
    
    # Load workflow state if available
    if [ -f "$CLAUDE_DIR/current-workflow-state.json" ]; then
        CURRENT_WORKFLOW=$(jq -r '.workflow // "unknown"' "$CLAUDE_DIR/current-workflow-state.json" 2>/dev/null)
        CURRENT_STAGE=$(jq -r '.currentStage // "unknown"' "$CLAUDE_DIR/current-workflow-state.json" 2>/dev/null)
    fi
    
    # Load agent context from workflow config
    if [ -f "$CLAUDE_DIR/workflow-config.json" ] && [ "$CURRENT_WORKFLOW" != "unknown" ] && [ "$CURRENT_STAGE" != "unknown" ]; then
        CURRENT_AGENT=$(jq -r ".workflows[\"$CURRENT_WORKFLOW\"].stages[] | select(.id==\"$CURRENT_STAGE\") | .agent // \"unknown\"" "$CLAUDE_DIR/workflow-config.json" 2>/dev/null)
    fi
}

# === SECURE NOTIFICATION FUNCTION ===
# Fixed security vulnerability - safely passes parameters to osascript
send_notification() {
    local title="$1"
    local message="$2"
    local sound="$3"
    
    # Detect platform and send appropriate notification
    case "$(uname)" in
        "Darwin")
            # macOS - Use osascript with safe parameter passing
            osascript -e 'on run {title, message, sound}' \
                      -e 'display notification message with title title sound name sound' \
                      -e 'end run' \
                      "$title" "$message" "$sound" 2>/dev/null || true
            ;;
        "Linux")
            # Linux - Use notify-send if available
            if command -v notify-send >/dev/null 2>&1; then
                notify-send "$title" "$message" 2>/dev/null || true
            else
                echo "NOTIFICATION [$title]: $message" >&2
            fi
            ;;
        *)
            # Fallback for other systems
            echo "NOTIFICATION [$title]: $message" >&2
            ;;
    esac
}

# === LOAD CONTEXT AND GENERATE NOTIFICATIONS ===
# Load workflow and agent context
load_context

# Initialize context-aware notification variables
CONTEXT_TITLE="$PROJECT_NAME"
CONTEXT_MESSAGE="Operation completed"
CONTEXT_SOUND="Glass"

# Add agent context to title if available
if [ -n "$CURRENT_AGENT" ] && [ "$CURRENT_AGENT" != "unknown" ]; then
    CONTEXT_TITLE="$CURRENT_AGENT - $PROJECT_NAME"
fi

# Determine notification based on event type and context
case "$EVENT_TYPE" in
    "PostToolUse")
        case "$TOOL_NAME" in
            "Write"|"Edit"|"MultiEdit")
                # Code changes detected - context-aware message
                if [ "$CURRENT_STAGE" = "review" ]; then
                    CONTEXT_MESSAGE="Code changes during review stage - May need re-review"
                    CONTEXT_SOUND="Ping"
                elif [ "$CURRENT_STAGE" = "development" ]; then
                    CONTEXT_MESSAGE="Development progress - Code updated"
                    CONTEXT_SOUND="Submarine"
                else
                    CONTEXT_MESSAGE="Code changes detected"
                    CONTEXT_SOUND="Ping"
                fi
                ;;
            "Bash")
                CONTEXT_MESSAGE="Command executed"
                CONTEXT_SOUND="Tink"
                ;;
            "TodoWrite")
                CONTEXT_MESSAGE="Todo list updated"
                CONTEXT_SOUND="Pop"
                ;;
            *)
                # Generic post-tool notification with context
                if [ -n "$CURRENT_STAGE" ] && [ "$CURRENT_STAGE" != "unknown" ]; then
                    CONTEXT_MESSAGE="Tool used in $CURRENT_STAGE stage"
                else
                    CONTEXT_MESSAGE="Tool operation completed"
                fi
                CONTEXT_SOUND="$NOTIFICATION_SOUND"
                ;;
        esac
        send_notification "$CONTEXT_TITLE" "$CONTEXT_MESSAGE" "$CONTEXT_SOUND"
        ;;
    "Stop")
        # Session completed with workflow context
        if [ -n "$CURRENT_STAGE" ] && [ "$CURRENT_STAGE" != "unknown" ]; then
            CONTEXT_MESSAGE="Session ended during $CURRENT_STAGE stage"
        else
            CONTEXT_MESSAGE="Coding session completed"
        fi
        send_notification "$CONTEXT_TITLE" "$CONTEXT_MESSAGE" "Glass"
        ;;
    "SubagentStop")
        # NEW: Handle SubagentStop events (missing from original)
        if [ -n "$CURRENT_AGENT" ] && [ "$CURRENT_AGENT" != "unknown" ]; then
            CONTEXT_MESSAGE="$CURRENT_AGENT subagent stopped"
        else
            CONTEXT_MESSAGE="Subagent stopped"
        fi
        send_notification "$CONTEXT_TITLE" "$CONTEXT_MESSAGE" "Basso"
        ;;
    "")
        # Handle cases where EVENT_TYPE is empty but HOOK_EVENT_NAME is provided
        if [ -n "$HOOK_EVENT_NAME" ]; then
            CONTEXT_MESSAGE="Hook event: $HOOK_EVENT_NAME"
            send_notification "$CONTEXT_TITLE" "$CONTEXT_MESSAGE" "Default"
        fi
        ;;
    *)
        # Default notification with better context
        CONTEXT_MESSAGE="${NOTIFICATION_MESSAGE:-Unknown event: $EVENT_TYPE}"
        send_notification "$CONTEXT_TITLE" "$CONTEXT_MESSAGE" "$NOTIFICATION_SOUND"
        ;;
esac

# CRITICAL: Pass through the JSON for the next hook/tool
# This prevents EPIPE errors
echo "$JSON_INPUT"

# Always exit 0 - notifications are informational only
exit 0