#!/bin/bash

# Workflow Context Evolution Engine
# Manages context updates and evolution during workflow execution

set -euo pipefail

# Configuration
# Find project root and .claude directory
find_project_root() {
    local dir="$(pwd)"
    while [ "$dir" != "/" ]; do
        if [ -d "$dir/.claude" ]; then
            echo "$dir"
            return 0
        fi
        dir=$(dirname "$dir")
    done
    return 1
}

PROJECT_ROOT=$(find_project_root)
if [ -z "$PROJECT_ROOT" ]; then
    echo "ERROR: Could not find project root with .claude directory" >&2
    exit 1
fi

CONTEXT_EVOLUTION_DIR="$PROJECT_ROOT/.claude/state/context-evolution"
WORKFLOW_CONTEXTS="$CONTEXT_EVOLUTION_DIR/workflow-contexts"
EVOLUTION_LOG="$CONTEXT_EVOLUTION_DIR/evolution.log"
CONTEXT_PATTERNS="$CONTEXT_EVOLUTION_DIR/patterns.json"

# Ensure directories exist
mkdir -p "$CONTEXT_EVOLUTION_DIR" "$WORKFLOW_CONTEXTS"

# Initialize evolution tracking
initialize_context_evolution() {
    if [[ ! -f "$CONTEXT_PATTERNS" ]]; then
        cat > "$CONTEXT_PATTERNS" << 'EOF'
{
  "evolution_patterns": {},
  "successful_transitions": [],
  "context_quality_metrics": {},
  "last_updated": null
}
EOF
    fi
    
    # Initialize log with headers
    if [[ ! -f "$EVOLUTION_LOG" ]]; then
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] Context Evolution Engine initialized" > "$EVOLUTION_LOG"
    fi
}

# Analyze context quality and relevance
analyze_context_quality() {
    local context_file="$1"
    local current_stage="$2"
    local task_description="$3"
    
    if [[ ! -f "$context_file" ]]; then
        echo "0.0"
        return 1
    fi
    
    local quality_score=0.0
    local relevance_score=0.0
    local freshness_score=0.0
    local completeness_score=0.0
    
    # Calculate relevance based on keyword matching
    local keywords
    keywords=$(echo "$task_description" | tr '[:upper:]' '[:lower:]' | grep -oE '\b\w{3,}\b' | head -10)
    local matched_keywords=0
    local total_keywords=0
    
    for keyword in $keywords; do
        total_keywords=$((total_keywords + 1))
        if grep -qi "$keyword" "$context_file"; then
            matched_keywords=$((matched_keywords + 1))
        fi
    done
    
    if [[ $total_keywords -gt 0 ]]; then
        relevance_score=$(echo "scale=2; $matched_keywords / $total_keywords" | bc -l)
    fi
    
    # Calculate freshness based on file modification time
    local file_age
    file_age=$(stat -c %Y "$context_file" 2>/dev/null || echo "0")
    local current_time
    current_time=$(date +%s)
    local age_hours=$(( (current_time - file_age) / 3600 ))
    
    if [[ $age_hours -lt 1 ]]; then
        freshness_score=1.0
    elif [[ $age_hours -lt 6 ]]; then
        freshness_score=0.8
    elif [[ $age_hours -lt 24 ]]; then
        freshness_score=0.6
    elif [[ $age_hours -lt 168 ]]; then  # 1 week
        freshness_score=0.4
    else
        freshness_score=0.2
    fi
    
    # Calculate completeness based on file size and structure
    local file_size
    file_size=$(wc -l < "$context_file" 2>/dev/null || echo "0")
    local sections
    sections=$(grep -c "^#" "$context_file" 2>/dev/null || echo "0")
    
    if [[ $file_size -gt 50 && $sections -gt 3 ]]; then
        completeness_score=1.0
    elif [[ $file_size -gt 20 && $sections -gt 1 ]]; then
        completeness_score=0.7
    elif [[ $file_size -gt 5 ]]; then
        completeness_score=0.4
    else
        completeness_score=0.1
    fi
    
    # Calculate overall quality score
    quality_score=$(echo "scale=3; ($relevance_score * 0.4) + ($freshness_score * 0.3) + ($completeness_score * 0.3)" | bc -l)
    
    echo "$quality_score"
}

# Update context with new findings
update_context_with_findings() {
    local workflow_id="$1"
    local stage="$2"
    local findings="$3"
    local agent_type="$4"
    local task_description="$5"
    
    local context_file="$WORKFLOW_CONTEXTS/${workflow_id}-context.md"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    # Create context file if it doesn't exist
    if [[ ! -f "$context_file" ]]; then
        cat > "$context_file" << EOF
# Workflow Context: $workflow_id
Created: $timestamp
Task: $task_description

## Initial Context
EOF
    fi
    
    # Calculate quality before update
    local quality_before
    quality_before=$(analyze_context_quality "$context_file" "$stage" "$task_description")
    
    # Add new findings
    cat >> "$context_file" << EOF

## Stage: $stage ($timestamp)
**Agent**: $agent_type
**Quality Score Before**: $quality_before

### Findings
$findings

### Context Evolution Notes
- Stage transition from previous to $stage
- Agent expertise: $agent_type
- Timestamp: $timestamp

---

EOF
    
    # Calculate quality after update
    local quality_after
    quality_after=$(analyze_context_quality "$context_file" "$stage" "$task_description")
    
    # Log evolution event
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] Context Evolution: $workflow_id | $stage | $agent_type | Quality: $quality_before -> $quality_after" >> "$EVOLUTION_LOG"
    
    # Update patterns file
    update_evolution_patterns "$stage" "$agent_type" "$quality_before" "$quality_after" "$findings"
    
    # Trigger context optimization if quality is declining
    if (( $(echo "$quality_after < $quality_before - 0.1" | bc -l) )); then
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: Context quality declining, triggering optimization" >> "$EVOLUTION_LOG"
        optimize_context "$context_file" "$stage" "$task_description"
    fi
    
    echo "$context_file"
}

# Optimize context by removing irrelevant information
optimize_context() {
    local context_file="$1"
    local current_stage="$2"
    local task_description="$3"
    
    local optimized_file="${context_file}.optimized"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] Optimizing context: $context_file" >> "$EVOLUTION_LOG"
    
    # Create optimized version
    cat > "$optimized_file" << EOF
# Optimized Workflow Context
Optimized: $timestamp
Stage: $current_stage
Task: $task_description

## Key Context (Relevance-Filtered)
EOF
    
    # Extract most relevant sections
    local keywords
    keywords=$(echo "$task_description" | tr '[:upper:]' '[:lower:]' | grep -oE '\b\w{3,}\b')
    
    # Keep header and recent updates
    head -20 "$context_file" >> "$optimized_file"
    
    echo "" >> "$optimized_file"
    echo "## Recent Relevant Updates" >> "$optimized_file"
    
    # Extract relevant content based on keywords
    for keyword in $keywords; do
        local relevant_content
        relevant_content=$(grep -A 5 -B 5 -i "$keyword" "$context_file" 2>/dev/null || echo "")
        if [[ -n "$relevant_content" ]]; then
            echo "" >> "$optimized_file"
            echo "### Content related to: $keyword" >> "$optimized_file"
            echo "$relevant_content" >> "$optimized_file"
        fi
    done
    
    # Keep the last 10 stage updates
    echo "" >> "$optimized_file"
    echo "## Recent Stage Updates" >> "$optimized_file"
    tail -50 "$context_file" | grep -A 10 "^## Stage:" | tail -30 >> "$optimized_file"
    
    # Replace original with optimized version
    mv "$optimized_file" "$context_file"
    
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] Context optimization completed" >> "$EVOLUTION_LOG"
}

# Update evolution patterns for learning
update_evolution_patterns() {
    local stage="$1"
    local agent_type="$2"
    local quality_before="$3"
    local quality_after="$4"
    local findings="$5"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    # Calculate quality change
    local quality_change
    quality_change=$(echo "scale=3; $quality_after - $quality_before" | bc -l)
    
    # Update patterns file
    jq --arg stage "$stage" --arg agent "$agent_type" --arg before "$quality_before" --arg after "$quality_after" --arg change "$quality_change" --arg findings "$findings" --arg ts "$timestamp" '
        .evolution_patterns[$stage + "_" + $agent] = {
            "average_quality_change": ((.evolution_patterns[$stage + "_" + $agent].average_quality_change // 0) + ($change | tonumber)) / 2,
            "last_quality_before": ($before | tonumber),
            "last_quality_after": ($after | tonumber),
            "last_findings": $findings,
            "update_count": ((.evolution_patterns[$stage + "_" + $agent].update_count // 0) + 1),
            "last_updated": $ts
        } |
        .last_updated = $ts
    ' "$CONTEXT_PATTERNS" > "$CONTEXT_PATTERNS.tmp" && mv "$CONTEXT_PATTERNS.tmp" "$CONTEXT_PATTERNS"
}

# Suggest context improvements
suggest_context_improvements() {
    local workflow_id="$1"
    local current_stage="$2"
    local task_description="$3"
    
    local context_file="$WORKFLOW_CONTEXTS/${workflow_id}-context.md"
    
    if [[ ! -f "$context_file" ]]; then
        echo "No context file found for workflow: $workflow_id"
        return 1
    fi
    
    local current_quality
    current_quality=$(analyze_context_quality "$context_file" "$current_stage" "$task_description")
    
    echo "# Context Improvement Suggestions"
    echo "Current Quality Score: $current_quality"
    echo ""
    
    # Analyze quality components
    if (( $(echo "$current_quality < 0.5" | bc -l) )); then
        echo "## High Priority Improvements"
        echo "- Add more relevant context related to the task"
        echo "- Update stale information"
        echo "- Include more specific technical details"
        echo ""
    fi
    
    if (( $(echo "$current_quality < 0.7" | bc -l) )); then
        echo "## Medium Priority Improvements"
        echo "- Organize context into clearer sections"
        echo "- Add cross-references between related concepts"
        echo "- Include examples and code snippets"
        echo ""
    fi
    
    # Suggest tools for improvement
    echo "## Recommended Tools for Context Enhancement"
    local jit_tool_selector="$PROJECT_ROOT/.claude/hooks/jit-tool-selector.sh"
    if [[ -x "$jit_tool_selector" ]]; then
        "$jit_tool_selector" suggest "improve context for $task_description" "context-engineer"
    else
        echo "- Use research tools to gather more relevant information"
        echo "- Apply analysis tools to identify context gaps"
        echo "- Leverage documentation tools for better structure"
    fi
}

# Generate context evolution report
generate_evolution_report() {
    local workflow_id="$1"
    local output_file="${2:-evolution-report.md}"
    
    local context_file="$WORKFLOW_CONTEXTS/${workflow_id}-context.md"
    
    if [[ ! -f "$context_file" ]]; then
        echo "No context file found for workflow: $workflow_id"
        return 1
    fi
    
    cat > "$output_file" << EOF
# Context Evolution Report: $workflow_id
Generated: $(date)

## Context Quality Evolution
EOF
    
    # Extract quality scores from log
    grep "Context Evolution: $workflow_id" "$EVOLUTION_LOG" | while IFS='|' read -r timestamp stage agent quality_change; do
        echo "- **Stage**: $stage, **Agent**: $agent, **Quality**: $quality_change" >> "$output_file"
    done
    
    cat >> "$output_file" << EOF

## Current Context Statistics
EOF
    
    # Add current file statistics
    local file_size
    file_size=$(wc -l < "$context_file")
    local sections
    sections=$(grep -c "^#" "$context_file" 2>/dev/null || echo "0")
    local updates
    updates=$(grep -c "^## Stage:" "$context_file" 2>/dev/null || echo "0")
    
    cat >> "$output_file" << EOF
- **Total Lines**: $file_size
- **Sections**: $sections
- **Stage Updates**: $updates
- **File Size**: $(du -h "$context_file" | cut -f1)

## Evolution Patterns
EOF
    
    # Add patterns from JSON
    if [[ -f "$CONTEXT_PATTERNS" ]]; then
        jq -r '.evolution_patterns | to_entries[] | "- **\(.key)**: Average quality change: \(.value.average_quality_change), Updates: \(.value.update_count)"' "$CONTEXT_PATTERNS" >> "$output_file"
    fi
    
    echo ""
    echo "Evolution report generated: $output_file"
}

# Prune context history to manage file size
prune_context_history() {
    local context_file="$1"
    local max_lines="${2:-1000}"
    
    if [[ ! -f "$context_file" ]]; then
        echo "Context file not found: $context_file"
        return 1
    fi
    
    local current_lines
    current_lines=$(wc -l < "$context_file")
    
    if [[ $current_lines -gt $max_lines ]]; then
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] Pruning context history: $context_file ($current_lines -> $max_lines lines)" >> "$EVOLUTION_LOG"
        
        local temp_file="${context_file}.pruned"
        
        # Keep header (first 50 lines) and tail (last N lines to reach max)
        local tail_lines=$((max_lines - 50))
        
        {
            head -50 "$context_file"
            echo ""
            echo "## [Context History Pruned - $(date)]"
            echo "Previous context history was pruned to maintain file size."
            echo ""
            tail -$tail_lines "$context_file"
        } > "$temp_file"
        
        mv "$temp_file" "$context_file"
        
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] Context pruning completed" >> "$EVOLUTION_LOG"
    fi
}

# Main function
main() {
    local action="${1:-help}"
    
    initialize_context_evolution
    
    case "$action" in
        "update")
            if [[ $# -ge 6 ]]; then
                update_context_with_findings "$2" "$3" "$4" "$5" "$6"
            else
                echo "Usage: $0 update <workflow_id> <stage> <findings> <agent_type> <task_description>"
                exit 1
            fi
            ;;
        "analyze")
            if [[ $# -ge 4 ]]; then
                analyze_context_quality "$2" "$3" "$4"
            else
                echo "Usage: $0 analyze <context_file> <stage> <task_description>"
                exit 1
            fi
            ;;
        "optimize")
            if [[ $# -ge 4 ]]; then
                optimize_context "$2" "$3" "$4"
            else
                echo "Usage: $0 optimize <context_file> <stage> <task_description>"
                exit 1
            fi
            ;;
        "suggest")
            if [[ $# -ge 4 ]]; then
                suggest_context_improvements "$2" "$3" "$4"
            else
                echo "Usage: $0 suggest <workflow_id> <stage> <task_description>"
                exit 1
            fi
            ;;
        "report")
            if [[ $# -ge 2 ]]; then
                generate_evolution_report "$2" "${3:-evolution-report.md}"
            else
                echo "Usage: $0 report <workflow_id> [output_file]"
                exit 1
            fi
            ;;
        "prune")
            if [[ $# -ge 2 ]]; then
                prune_context_history "$2" "${3:-1000}"
            else
                echo "Usage: $0 prune <context_file> [max_lines]"
                exit 1
            fi
            ;;
        "help"|*)
            cat << EOF
Workflow Context Evolution Engine

Usage: $0 <action> [arguments...]

Actions:
  update <workflow_id> <stage> <findings> <agent_type> <task_description>
    Update context with new findings from a workflow stage
    
  analyze <context_file> <stage> <task_description>
    Analyze context quality and relevance
    
  optimize <context_file> <stage> <task_description>
    Optimize context by removing irrelevant information
    
  suggest <workflow_id> <stage> <task_description>
    Suggest context improvements
    
  report <workflow_id> [output_file]
    Generate context evolution report
    
  prune <context_file> [max_lines]
    Prune context history to manage file size
    
  help
    Show this help message

Examples:
  $0 update dev-123 implementation "Added auth system" james "implement authentication"
  $0 analyze /path/to/context.md review "code review session"
  $0 suggest dev-123 testing "test authentication system"
  $0 report dev-123 auth-evolution-report.md
EOF
            ;;
    esac
}

# Execute main function if script is run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi