#!/bin/bash

# Auto Review Agent Hook
# Automatically invokes the code review agent when triggered by orchestration

# Set error handling
set -o pipefail
trap '' PIPE

# === READ AND PASS THROUGH JSON FIRST ===
# Read JSON input from stdin (REQUIRED for all Claude Code hooks)
if ! JSON_INPUT=$(cat 2>/dev/null); then
    # If we can't read stdin, exit gracefully
    exit 0
fi

# === DEPENDENCY VALIDATION ===
if ! command -v jq >/dev/null 2>&1; then
    # Pass through JSON and exit if jq not available
    echo "$JSON_INPUT"
    exit 0
fi

# Find .claude directory
find_claude_dir() {
    local dir="$(pwd)"
    while [ "$dir" != "/" ]; do
        if [ -d "$dir/.claude" ]; then
            echo "$dir/.claude"
            return 0
        fi
        dir=$(dirname "$dir")
    done
    return 1
}

CLAUDE_DIR=$(find_claude_dir)
if [ -z "$CLAUDE_DIR" ]; then
    echo "$JSON_INPUT"
    exit 0
fi

REVIEW_TRIGGER_FILE="$CLAUDE_DIR/review-trigger.json"
ORCHESTRATION_LOG="$CLAUDE_DIR/orchestration.log"

# Check if review is triggered - if not, just pass through JSON
if [ ! -f "$REVIEW_TRIGGER_FILE" ]; then
    echo "$JSON_INPUT"
    exit 0
fi

# Read trigger context
TRIGGER_CONTEXT=$(cat "$REVIEW_TRIGGER_FILE" 2>/dev/null)
if [ -z "$TRIGGER_CONTEXT" ]; then
    exit 0
fi

# Extract context information
TOOL_NAME=$(echo "$TRIGGER_CONTEXT" | jq -r '.toolName // "unknown"' 2>/dev/null)
PROJECT_NAME=$(echo "$TRIGGER_CONTEXT" | jq -r '.context.projectName // "unknown"' 2>/dev/null)
WORKING_DIR=$(echo "$TRIGGER_CONTEXT" | jq -r '.context.workingDirectory // "."' 2>/dev/null)

# Log the review initiation
echo "$(date): Auto-review triggered for tool: $TOOL_NAME in project: $PROJECT_NAME" >> "$ORCHESTRATION_LOG"

# === INTELLIGENT FILE DETECTION ===
# Detect which files were likely modified based on recent git changes
get_recently_modified_files() {
    local files=""
    
    # Try to get files from git (staged and unstaged changes)
    if command -v git >/dev/null 2>&1 && git rev-parse --git-dir >/dev/null 2>&1; then
        # Get staged files
        local staged_files=$(git diff --cached --name-only 2>/dev/null)
        # Get unstaged files
        local unstaged_files=$(git diff --name-only 2>/dev/null)
        # Get untracked files (recent ones)
        local untracked_files=$(git ls-files --others --exclude-standard 2>/dev/null | head -10)
        
        files="$staged_files $unstaged_files $untracked_files"
    fi
    
    # If no git files found, use recently modified files
    if [ -z "$files" ]; then
        files=$(find . -name "*.js" -o -name "*.ts" -o -name "*.py" -o -name "*.md" -o -name "*.json" -o -name "*.yml" -o -name "*.yaml" | head -20 2>/dev/null)
    fi
    
    echo "$files" | tr ' ' '\n' | grep -v "^$" | head -10
}

# === SMART REVIEW SCOPE DETERMINATION ===
determine_review_scope() {
    local modified_files=$(get_recently_modified_files)
    local scope="quality"  # default scope
    
    # Analyze file types to determine appropriate review focus
    if echo "$modified_files" | grep -q -E "\.(py|js|ts|go|java|cpp|c)$"; then
        scope="implementation"
    elif echo "$modified_files" | grep -q -E "\.(yml|yaml|json|config)$"; then
        scope="configuration"
    elif echo "$modified_files" | grep -q -E "\.(md|txt|rst)$"; then
        scope="documentation"
    elif echo "$modified_files" | grep -q -E "(test|spec)"; then
        scope="testing"
    fi
    
    echo "$scope"
}

# === CODE REVIEW AGENT INVOCATION ===
invoke_code_review() {
    local modified_files=$(get_recently_modified_files)
    local review_scope=$(determine_review_scope)
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    # Create review context file
    local review_context_file="$CLAUDE_DIR/current-review-context.json"
    cat > "$review_context_file" <<EOF
{
  "reviewId": "auto-review-$(date +%s)",
  "timestamp": "$timestamp",
  "trigger": "automatic",
  "toolName": "$TOOL_NAME",
  "scope": "$review_scope",
  "files": [
$(echo "$modified_files" | sed 's/.*/"&"/' | paste -sd ',' -)
  ],
  "projectContext": {
    "name": "$PROJECT_NAME",
    "workingDirectory": "$WORKING_DIR"
  },
  "reviewCriteria": {
    "leverCompliance": true,
    "codeQuality": true,
    "bmadStandards": true,
    "autoFix": false
  }
}
EOF
    
    # Log the review details
    echo "$(date): Initiating code review with scope: $review_scope" >> "$ORCHESTRATION_LOG"
    echo "$(date): Files for review: $(echo $modified_files | tr '\n' ' ')" >> "$ORCHESTRATION_LOG"
    
    # Create review instruction for next Claude interaction
    local review_instruction_file="$CLAUDE_DIR/pending-review-instruction.md"
    cat > "$review_instruction_file" <<EOF
# Automatic Code Review Required

## Context
- **Triggered by**: $TOOL_NAME operation
- **Project**: $PROJECT_NAME
- **Review Scope**: $review_scope
- **Timestamp**: $timestamp

## Files to Review
$(echo "$modified_files" | sed 's/^/- /')

## Review Instructions
Please perform a comprehensive code review using the BMAD code review standards:

\`\`\`bash
*codereview $(echo "$modified_files" | tr '\n' ' ') $review_scope
\`\`\`

## Expected Review Focus
- LEVER framework compliance
- Code quality and maintainability
- BMAD standards adherence
- Configuration safety and best practices

## Next Steps
After review completion:
1. Provide specific improvement recommendations
2. Mark any critical issues that need immediate attention
3. Trigger change implementation if modifications are needed

---
*Generated by BMAD Agent Orchestration System*
EOF
    
    # Create a notification for the user
    echo "$(date): Code review instruction created at: $review_instruction_file" >> "$ORCHESTRATION_LOG"
    
    return 0
}

# === MAIN EXECUTION ===
# Invoke the code review process
invoke_code_review

# Clean up the trigger file
rm -f "$REVIEW_TRIGGER_FILE"

# Mark review as initiated
echo "$(date): Auto-review agent completed initialization" >> "$ORCHESTRATION_LOG"

# CRITICAL: Always pass through the JSON for the next hook/tool
echo "$JSON_INPUT"

exit 0