#!/bin/bash

# Hook script to read and display workflow + LEVER principles before coding starts
# Claude Code compatible - reads JSO<PERSON> from stdin

# Read JSON input from stdin (required for Claude Code hooks)
JSON_INPUT=$(cat)

# Extract tool name
TOOL_NAME=$(echo "$JSON_INPUT" | jq -r '.toolName // empty' 2>/dev/null)

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CLAUDE_DIR="$(dirname "$SCRIPT_DIR")"

WORKFLOW_CONFIG="$CLAUDE_DIR/workflow-config.json"
WORKFLOW_LOG="$CLAUDE_DIR/workflow.log"
LEVER_REMINDER="$CLAUDE_DIR/lever-reminder-shown"

# Function to log workflow events
log_workflow() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1" >> "$WORKFLOW_LOG"
}

# Check if workflow config exists
if [ ! -f "$WORKFLOW_CONFIG" ]; then
    echo "Workflow configuration not found at $WORKFLOW_CONFIG" >&2
    exit 0  # Non-blocking, just continue
fi

# Read active workflow
ACTIVE_WORKFLOW=$(jq -r '.activeWorkflow' "$WORKFLOW_CONFIG")
WORKFLOW_NAME=$(jq -r ".workflows.\"$ACTIVE_WORKFLOW\".name" "$WORKFLOW_CONFIG")

# Display LEVER principles prominently
echo ""
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "                    🎯 LEVER FRAMEWORK REMINDER"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""
echo "⚡ CORE PRINCIPLE:"
echo "   \"The best code is no code. The second best code is code that already exists and works.\""
echo ""
echo "📋 BEFORE WRITING ANY CODE, ASK:"
echo "   ✓ Can I extend an existing table instead of creating a new one?"
echo "   ✓ Can I add to an existing query instead of a new query?"
echo "   ✓ Can I enhance an existing hook instead of a new hook?"
echo "   ✓ Can I modify an existing component instead of a new component?"
echo ""
echo "🔤 LEVER CHECKLIST:"
echo "   L - Leverage existing patterns (What similar code exists?)"
echo "   E - Extend before creating (Can I add to what's there?)"
echo "   V - Verify through reactivity (Use framework features)"
echo "   E - Eliminate duplication (Don't repeat code)"
echo "   R - Reduce complexity (Keep it simple)"
echo ""
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""

# Display workflow information
echo "📊 ACTIVE WORKFLOW: $WORKFLOW_NAME"
echo ""
echo "Workflow Stages:"
jq -r ".workflows.\"$ACTIVE_WORKFLOW\".stages[] | \"  [\(.id)] \\(.name)\"" "$WORKFLOW_CONFIG"
echo ""
echo "Current Stage: Pre-Implementation Analysis"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""

# Create/update workflow state file
WORKFLOW_STATE="$CLAUDE_DIR/current-workflow-state.json"
if [ ! -f "$WORKFLOW_STATE" ]; then
    echo "{
  \"workflow\": \"$ACTIVE_WORKFLOW\",
  \"currentStage\": \"pre-implementation\",
  \"iteration\": 1,
  \"startTime\": \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\",
  \"leverAnalysisRequired\": true,
  \"lastLeverReminder\": \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\"
}" > "$WORKFLOW_STATE"
else
    # Update last reminder time
    jq ".lastLeverReminder = \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\"" "$WORKFLOW_STATE" > "$WORKFLOW_STATE.tmp" && mv "$WORKFLOW_STATE.tmp" "$WORKFLOW_STATE"
fi

# Send notification
osascript -e 'display notification "LEVER Framework Active - Extend existing code!" with title "BMAD Pre-Coding" sound name "Purr"' 2>/dev/null || true

# Log
log_workflow "LEVER principles displayed for tool: $TOOL_NAME"

# Mark that reminder was shown
touch "$LEVER_REMINDER"

# CRITICAL: Pass through the JSON for the next hook/tool
echo "$JSON_INPUT"

exit 0