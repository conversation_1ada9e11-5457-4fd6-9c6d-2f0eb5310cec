{"workflows": {"dev-review-rewrite": {"name": "Development Review Rewrite Cycle with LEVER Optimization", "description": "Standard workflow for development with LEVER framework optimization, code review, and potential rewrite phases", "optimizationFramework": "LEVER", "stages": [{"id": "planning", "name": "LEVER-Aware Planning", "agent": "Developer", "description": "Plan implementation with LEVER principles in mind", "leverFocus": ["Identify existing code to leverage", "Find extension opportunities", "Minimize new code creation", "Simplify approach"], "next": "pre-implementation", "notifications": {"onStart": "Starting LEVER-aware planning phase", "onComplete": "Planning complete, moving to analysis"}}, {"id": "pre-implementation", "name": "Pre-Implementation Analysis", "agent": "Developer", "description": "LEVER framework analysis before writing any code", "checks": ["pattern-recognition", "complexity-assessment", "reuse-opportunities"], "next": "development", "notifications": {"onStart": "Starting LEVER pre-implementation analysis", "onComplete": "Analysis complete, proceeding to development"}}, {"id": "development", "name": "Development", "agent": "Developer", "description": "Implementation following LEVER principles", "leverPrinciples": ["Leverage existing patterns", "Extend before creating", "Verify through reactivity", "Eliminate duplication", "Reduce complexity"], "next": "review", "notifications": {"onStart": "Starting development phase with LEVER optimization", "onComplete": "Development complete, sending to review"}}, {"id": "review", "name": "Code Review with LEVER Verification", "agent": "Code Reviewer", "description": "Review implementation for quality, standards, LEVER compliance, and requirements", "leverChecks": ["Did the code leverage existing patterns?", "Were existing components extended vs creating new?", "Is there unnecessary duplication?", "Was complexity minimized?", "Are reactive patterns used effectively?"], "next": {"approved": "complete", "needsChanges": "rewrite", "majorRevision": "pre-implementation"}, "notifications": {"onStart": "Starting code review with LEVER optimization checks", "onApprove": "Code review passed - LEVER compliant", "onReject": "Code review requested changes"}}, {"id": "rewrite", "name": "Rewrite/Fix", "agent": "Change Implementation Agent", "description": "Address reviewer feedback and improve implementation", "next": "review", "notifications": {"onStart": "Addressing review feedback", "onComplete": "Changes complete, returning to review"}}, {"id": "complete", "name": "Complete", "description": "Task successfully completed", "notifications": {"onReach": "Task completed successfully!"}}], "maxIterations": 3, "escalationStrategy": "If review-rewrite cycle exceeds maxIterations, escalate to senior developer"}}, "activeWorkflow": "dev-review-rewrite"}