---
description: Execute project course correction using Scrum Master expertise for strategic realignment
---

# PIB Correct Course Command

**Usage**: `*correct-course [reason] [options]`

## Purpose

Executes strategic project course correction using Scrum Master expertise, leveraging PIB principles to pivot project direction while preserving valuable work and extending successful patterns.

### Key Benefits
- Leverages existing project assets while redirecting efforts efficiently
- Extends successful patterns from current work into new direction
- Reduces waste by preserving valuable analysis and implementation
- Integrates course correction seamlessly with PIB workflow management

### When to Use
- When project requirements have significantly changed
- After discovering major technical or business blockers
- When user feedback indicates fundamental direction problems
- As part of agile retrospective and pivoting processes

## Implementation

### Step-by-Step Algorithm

#### 1. LEVER Framework Pre-Validation
Before executing, validate these principles:
- **L** - Leverage: Existing analysis, code, and documentation that remains valuable
- **E** - Extend: Successful patterns and components into new direction
- **V** - Verify: New direction against project goals and constraints
- **E** - Eliminate: Obsolete requirements and blocking assumptions
- **R** - Reduce: Complexity and risk in the pivot process

#### 2. Context Gathering
```bash
# Load current project state and issues
CURRENT_AGENT=$(cat .claude/current-workflow-state.json | jq -r '.currentAgent // "scrum-master"')
PROJECT_CONTEXT=$(cat .ai/project-context.md 2>/dev/null || echo "No project context available")
CURRENT_PRD=$(cat docs/prd.md 2>/dev/null || echo "No PRD found")
CURRENT_STORIES=$(ls docs/stories/ 2>/dev/null || echo "No stories found")
```

#### 3. Agent-Specific Processing
Adapt behavior based on active agent persona:

##### If Scrum Master Active
- Focus on workflow and process realignment
- Sprint planning adjustments and backlog reorganization
- Team coordination and communication strategies

##### If PM Active  
- Emphasize business alignment and stakeholder communication
- Requirements analysis and priority reassessment
- Risk mitigation and timeline adjustments

#### 4. Core Processing
- Load Scrum Master persona and course correction task
- Analyze current project state and identify preservation opportunities
- Document reasons for course correction and new strategic direction
- Create transition plan preserving valuable work while eliminating blockers
- Update project documentation and workflow state

#### 5. Output Generation
- Course correction analysis saved to `docs/course-correction-analysis.md`
- Updated project brief and PRD reflecting new direction
- Transition plan with work preservation strategies
- Updated workflow state and agent context

#### 6. Workflow Integration
- Updates workflow state to reflect strategic pivot
- Triggers knowledge update to redistribute new direction
- Prepares context for story rewriting and backlog reorganization

## Quality Features / Validation Checklist

### PIB Compliance Validation
- [ ] **LEVER Principles**: Preserves valuable work while eliminating blocking elements
- [ ] **Agent Alignment**: Uses Scrum Master process expertise for course correction
- [ ] **Workflow Integration**: Maintains workflow continuity through transition
- [ ] **Knowledge Capture**: Documents pivot rationale and preservation decisions
- [ ] **Context Awareness**: Builds upon existing project analysis and implementation

### Technical Quality Gates
- [ ] **Input Validation**: Handles incomplete project state gracefully
- [ ] **Error Handling**: Provides guidance when project assets are missing
- [ ] **Performance**: Efficient analysis and transition planning process
- [ ] **Consistency**: Maintains project coherence through directional change
- [ ] **Documentation**: Clear transition plan with actionable steps

### Output Quality Assurance
- [ ] **Completeness**: Addresses all aspects of project direction change
- [ ] **Accuracy**: Transition plan is realistic and achievable
- [ ] **Relevance**: New direction aligns with business goals and constraints
- [ ] **Actionability**: Provides specific steps for implementing course correction
- [ ] **Integration**: Seamlessly transitions existing work into new direction

### Knowledge Management Standards
- [ ] **Persistence**: Saves course correction analysis and transition plan
- [ ] **Versioning**: Maintains history of strategic decisions and changes
- [ ] **Cross-Reference**: Links preserved work with new direction requirements
- [ ] **Searchability**: Structured for easy reference during transition
- [ ] **Agent Context**: Updates all agents with new strategic direction

## Integration

### Workflow Hooks
- **Triggers**: `read-workflow.sh` for LEVER principles validation
- **Updates**: `workflow-transition.sh` for strategic state change
- **Notifications**: `notification-hook.sh` for course correction alerts
- **Context**: Updates `.claude/current-workflow-state.json` with new direction

### Knowledge Management Integration
- **Saves**: Course correction analysis to `docs/course-correction-analysis.md`
- **Updates**: Project context with new direction in `.ai/project-context.md`
- **Creates**: Transition tracking in `.ai/course-corrections/` directory
- **Links**: Maps preserved assets to new requirements

### Agent Context Updates
- **Scrum Master**: Updates process and workflow management knowledge
- **All Agents**: Updates shared project direction and priorities
- **PM**: Updates business alignment and stakeholder communication
- **Development**: Prepares new implementation priorities and preserved assets

### Follow-up Command Preparation
Results prepare context for:
- `*update-knowledge` - Distribute new strategic direction to all agents
- `*pm-prd` - Update PRD with new requirements and preserved elements
- `*create-next-story` - Rewrite stories for new direction
- `*doc-cleanup` - Reorganize documentation for new strategic focus

## Related Commands

### Core Management Commands
- `*plan-workflow` - Replan workflows for new strategic direction
- `*update-knowledge` - Essential for distributing course correction
- `*doc-cleanup` - Reorganize documentation for new focus

### Workflow Commands
- `*pm-prd` - Update requirements document for new direction
- `*create-next-story` - Rewrite user stories for pivot
- `*task-orchestration` - Coordinate new implementation priorities

### Agent Commands
- `/switch-agent scrum-master` - Optimal agent for course correction management
- `/switch-agent pm` - Business alignment perspective for course correction

### Knowledge Commands
- `/update-knowledge` - Critical for distributing new strategic direction
- `/memory-extract` - Extract valuable insights from current work

## Example Usage

### Basic Usage
```bash
# Execute course correction for current project
*correct-course "Requirements changed after stakeholder feedback"
```

### Advanced Usage
```bash
# Course correction with specific preservation focus
*correct-course "Technical blocker discovered" --preserve=frontend --pivot=backend
```

### Agent-Specific Usage
```bash
# When Scrum Master agent is active:
*correct-course "Sprint retrospective identified major issues" --process-focused

# When PM agent is active:
*correct-course "Business priorities shifted" --stakeholder-communication
```

### Workflow Integration Example
```bash
# During project execution when pivot is needed:
/switch-agent scrum-master
*correct-course "User testing revealed fundamental UX issues"
/update-knowledge
/pm-prd  # Update requirements
/create-next-story  # Rewrite stories for new direction
```

## Notes

### Agent Personas
This command adapts its behavior based on the active agent:
- **Scrum Master**: Process-focused course correction with workflow optimization
- **PM**: Business-aligned pivot with stakeholder communication strategies
- **Architect**: Technical feasibility assessment for new direction
- **Analyst**: Market research validation for strategic changes
- **QA**: Quality assurance continuity through directional changes

### LEVER Framework Emphasis
Every execution must demonstrate:
- **Leverage**: Preserve valuable analysis, code, and documentation from current work
- **Extend**: Successful patterns and components into new strategic direction
- **Verify**: New direction against updated business goals and technical constraints
- **Eliminate**: Blocking requirements, obsolete assumptions, and problematic elements
- **Reduce**: Risk and waste in the pivot process while maintaining team momentum

### Best Practices
- Document clear rationale for course correction to maintain team alignment
- Identify and preserve valuable work that applies to new direction
- Communicate changes to all stakeholders before implementing transition
- Use incremental approach to minimize disruption and validate new direction