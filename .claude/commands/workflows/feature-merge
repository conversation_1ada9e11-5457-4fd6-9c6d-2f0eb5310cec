#!/bin/bash

# Feature Merge Command Handler
# Merges your feature worktree changes back to the main branch

set -euo pipefail

# Find project root dynamically
find_project_root() {
    local dir="$(pwd)"
    while [ "$dir" != "/" ]; do
        if [ -d "$dir/.claude" ]; then
            echo "$dir"
            return 0
        fi
        dir=$(dirname "$dir")
    done
    return 1
}

PROJECT_ROOT=$(find_project_root)
if [ -z "$PROJECT_ROOT" ]; then
    echo "ERROR: Could not find project root with .claude directory" >&2
    exit 1
fi

# Get optional parameters
TARGET_BRANCH="${1:-main}"
AUTO_CLEANUP="${2:-true}"

# Handle no-cleanup flag
if [[ "$TARGET_BRANCH" == "no-cleanup" ]]; then
    TARGET_BRANCH="main"
    AUTO_CLEANUP="false"
elif [[ "$AUTO_CLEANUP" == "no-cleanup" ]]; then
    AUTO_CLEANUP="false"
fi

# Call the worktree manager with dynamic path
"$PROJECT_ROOT/.claude/hooks/worktree-manager.sh" merge "" "$TARGET_BRANCH" "$AUTO_CLEANUP"