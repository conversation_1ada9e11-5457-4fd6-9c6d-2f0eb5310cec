# Clean Up Completed Feature Worktrees

Removes completed feature worktrees and archives their state for future reference.

## Usage
```
/feature-cleanup [feature-name] [options]
```

## Parameters
- `feature-name`: Specific feature to cleanup (optional, will prompt if not provided)
- `options`: Additional cleanup options

## Available Options
- `--force`: Remove worktree even with uncommitted changes
- `--keep-state`: Archive state but don't remove from active index
- `--dry-run`: Show what would be cleaned without actual removal
- `--all-stale`: Remove all worktrees inactive for 7+ days

## What This Command Does

### 1. Pre-Cleanup Validation
- Checks for uncommitted changes
- Validates worktree exists and is accessible
- Ensures you're not currently in the target worktree
- Creates backup of current state

### 2. State Archival
- Archives complete state directory with timestamp
- Preserves Context Engineering evolution chains
- Saves tool selection patterns and effectiveness data
- Maintains MCP conversation history for analysis

### 3. Worktree Removal
- Removes Git worktree reference
- Deletes worktree directory and contents
- Updates PIB worktree index
- Cleans up orphaned Git references

### 4. Cleanup Verification
- Validates complete removal
- Updates tracking databases
- Generates cleanup report
- Suggests follow-up actions

## Examples

### Basic Cleanup
```
/feature-cleanup user-authentication
```
Removes completed user-authentication worktree

### Interactive Cleanup
```
/feature-cleanup
```
Prompts to select from active worktrees

### Force Cleanup (Uncommitted Changes)
```
/feature-cleanup payment-gateway --force
```
Removes worktree even with uncommitted changes

### Dry Run
```
/feature-cleanup user-profile --dry-run
```
Shows what would be cleaned without actually doing it

### Clean All Stale Worktrees
```
/feature-cleanup --all-stale
```
Removes all worktrees inactive for 7+ days

### Keep State for Analysis
```
/feature-cleanup analytics-feature --keep-state
```
Archives state but keeps in active tracking

## Interactive Mode

When run without feature name:
```
🧹 Feature Cleanup - Select worktree to remove:

1. user-auth (Clean - 3 days old)
2. payment-sys (Modified - 1 day old) ⚠️
3. mobile-ui (Clean - 1 week old)
4. api-refactor (Clean - 2 weeks old)

Enter number (1-4) or 'q' to quit: 
```

### Selection Indicators
- **Clean**: Safe to remove, no uncommitted changes
- **Modified**: Has uncommitted changes (requires --force)
- **⚠️**: Warning indicator for worktrees with changes

## Safety Features

### Change Detection
- Scans for uncommitted changes (staged and unstaged)
- Checks for untracked files
- Warns about potential data loss
- Requires explicit confirmation for risky operations

### State Preservation
- Complete state backup before removal
- Timestamped archives in `.claude/state/archived/`
- Recovery instructions in cleanup log
- Restoration capability for 90 days

### Validation Checks
- Ensures worktree is not currently active
- Validates Git worktree status
- Checks for file system consistency
- Prevents removal of main repository

## Archival System

### Archive Structure
```
.claude/state/archived/worktrees/
├── user-auth-20250704-143052/        # Timestamped archive
│   ├── context-engine/               # Context Engineering state
│   ├── context-evolution/           # Evolution chains
│   ├── mcp-conversations/           # MCP history
│   ├── tool-selection/              # Tool selection cache
│   └── cleanup-metadata.json       # Cleanup information
```

### Archive Metadata
```json
{
  "feature_name": "user-auth",
  "cleanup_date": "2025-07-04T14:30:52Z",
  "worktree_directory": "/Projects/pib-user-auth",
  "branch_name": "feature/user-auth-20250704",
  "final_commit": "abc123def456",
  "state_size": "15MB",
  "restoration_command": "/.claude/hooks/state-isolator.sh restore /path/to/archive"
}
```

## Cleanup Report

After successful cleanup:
```
✅ Feature Cleanup Complete: user-authentication

📊 Cleanup Summary:
   Feature: user-authentication
   Branch: feature/user-authentication-20250704
   Worktree: /Projects/pib-user-authentication (removed)
   State: Archived to .claude/state/archived/worktrees/user-authentication-20250704-143052

📈 Statistics:
   Context Evolution Chains: 8 (archived)
   Tool Selections: 23 (patterns saved)
   MCP Conversations: 5 (history preserved)
   Total State Size: 12.3 MB

🔄 Recovery:
   State can be restored within 90 days using:
   /.claude/hooks/state-isolator.sh restore .claude/state/archived/worktrees/user-authentication-20250704-143052

⚡ Next Steps:
   - Main branch has been updated with merged changes
   - Archive will be auto-cleaned after 90 days
   - Use /feature-list to see remaining active worktrees
```

## Troubleshooting

### "Worktree has uncommitted changes"
```bash
# Option 1: Commit changes first
cd ../pib-feature-name
git add .
git commit -m "Final changes"
/feature-cleanup feature-name

# Option 2: Force cleanup (loses changes)
/feature-cleanup feature-name --force
```

### "Cannot remove current worktree"
- You're currently in the worktree directory
- Switch to main repository or different worktree
- Then run cleanup command

### "Worktree not found"
```bash
# Check what worktrees exist
/feature-list

# Clean up orphaned references
git worktree prune

# Use worktree manager directly
/.claude/hooks/worktree-manager.sh cleanup
```

## Advanced Usage

### Batch Cleanup
```bash
# Clean all completed features (interactive)
for feature in $(git branch -r | grep feature/ | grep -E '(week|month)'); do
    /feature-cleanup $(basename $feature) --dry-run
done
```

### Conditional Cleanup
```bash
# Clean only if merged to main
if git branch --merged main | grep -q feature/my-feature; then
    /feature-cleanup my-feature
fi
```

### Archive Management
```bash
# List all archives
ls -la .claude/state/archived/worktrees/

# Find archives older than 30 days
find .claude/state/archived/worktrees/ -type d -mtime +30

# Manual archive restoration
/.claude/hooks/state-isolator.sh restore .claude/state/archived/worktrees/feature-20250601-120000
```

## Automatic Cleanup

### Scheduled Cleanup (Optional)
Add to your shell profile for automatic cleanup:
```bash
# ~/.bashrc or ~/.zshrc
pib_cleanup_check() {
    if [ -d ".claude" ]; then
        local stale=$(/.claude/hooks/worktree-manager.sh list | grep -c "week\|month")
        if [ "$stale" -gt 0 ]; then
            echo "💡 Found $stale stale worktree(s). Run '/feature-cleanup --all-stale' to clean up."
        fi
    fi
}

# Run check when entering PIB projects
alias cd='cd_with_pib_check() { builtin cd "$@" && pib_cleanup_check; }; cd_with_pib_check'
```

### Integration with Git Hooks
```bash
# .git/hooks/post-merge (make executable)
#!/bin/bash
if [ -f ".claude/hooks/worktree-manager.sh" ]; then
    # Auto-suggest cleanup after merging features
    echo "💡 Consider running '/feature-cleanup' to clean up merged features"
fi
```

## Related Commands
- `/feature-list` - See active worktrees before cleanup
- `/feature-merge` - Merge before cleanup
- `/feature-start` - Create new worktree after cleanup

## Performance Considerations

### Large Worktrees
- Cleanup time scales with worktree size
- State archival may take longer for complex features
- Network operations not required (local only)

### Storage Management
- Archives consume disk space (typically 5-50MB each)
- Automatic cleanup after 90 days
- Manual cleanup available for immediate space recovery

---
**Note**: This command provides safe and comprehensive cleanup of completed feature development with full state archival and recovery capabilities.