---
description: Generate detailed user stories from epics using MCP analysis and sub-agent assignment
---

# Epic to Stories - Automated Story Generation

**Usage**: `/epic-to-stories $ARGUMENTS`

Generate stories from epic file: $ARGUMENTS

*thinkdeep epic-file=$ARGUMENTS model=gemini-2.5-pro thinking_mode=high step=1 step_number=1 total_steps=6 next_step_required=true findings="Analyzing epic file: $ARGUMENTS to generate comprehensive user stories with sub-agent assignments using LEVER framework principles and MCP tool integration."

## What This Command Does

### 1. Epic Analysis with MCP Tools
- **Zen MCP Deep Analysis**: Comprehensive epic breakdown using thinkdeep
- **Context7 MCP Research**: Research implementation patterns and similar solutions
- **Story Identification**: Intelligent story extraction from epic requirements
- **Sub-Agent Assignment**: Automatic sub-agent assignment based on story characteristics

### 2. Story File Generation
- **Template-Based Generation**: Uses existing PIB story template structure
- **Sub-Agent Integration**: Pre-populates sub-agent assignments and workflows
- **Dependency Mapping**: Identifies and maps story dependencies
- **Quality Criteria**: Generates story-specific acceptance criteria

### 3. LEVER Framework Integration
- **Leverage Identification**: Identifies existing functionality to leverage per story
- **Extension Opportunities**: Maps extension points for each story
- **Verification Planning**: Plans verification strategies for each story
- **Duplication Prevention**: Identifies and prevents duplicate functionality
- **Complexity Reduction**: Optimizes story complexity and implementation approach

### 4. Planning Integration
- **Planning Index Updates**: Updates planning index with generated stories
- **Context Bridge Integration**: Links stories to planning context
- **Orchestration Preparation**: Prepares stories for orchestration planning

## Implementation Process

### Phase 1: Epic Analysis and Understanding
```
Epic File → Content Analysis → MCP Deep Analysis → Requirements Extraction → Context Building
```

1. **Epic Content Parsing**: Extract epic goals, requirements, and success criteria
2. **Zen MCP Analysis**: Deep thinking analysis of epic scope and complexity
3. **Context7 Research**: Research similar implementations and best practices
4. **Requirement Categorization**: Categorize requirements by domain (UI, API, data, etc.)
5. **Complexity Assessment**: Evaluate overall epic complexity and implementation challenges

### Phase 2: Story Breakdown and Categorization
```
Requirements → Story Identification → Story Categorization → Sub-Agent Assignment → Dependency Analysis
```

1. **Story Extraction**: Identify logical story units from epic requirements
2. **Story Categorization**: Categorize stories by type (frontend, backend, devops, testing)
3. **Priority Assessment**: Evaluate story priority based on business value and dependencies
4. **Sub-Agent Assignment**: Assign stories to appropriate sub-agents based on expertise
5. **Dependency Mapping**: Identify inter-story dependencies and prerequisites

### Phase 3: Story File Generation
```
Story Breakdown → Template Application → Content Generation → Sub-Agent Integration → File Creation
```

1. **Template Loading**: Load PIB story template structure
2. **Content Population**: Populate template with story-specific content
3. **Sub-Agent Integration**: Add sub-agent assignment and workflow details
4. **Acceptance Criteria**: Generate specific, testable acceptance criteria
5. **Technical Guidance**: Add implementation guidance and technical notes

### Phase 4: Planning Integration and Documentation
```
Generated Stories → Planning Index Update → Context Bridge → Documentation → Validation
```

1. **Planning Index Update**: Register stories in planning system
2. **Context Bridge Creation**: Link stories to epic planning context
3. **Generation Documentation**: Create story generation results document
4. **Quality Validation**: Validate story quality and completeness

## Command Parameters and Options

### Basic Usage
```bash
# Generate stories from epic file
/epic-to-stories docs/epics/user-management-epic.md

# Generate with specific output directory
/epic-to-stories docs/epics/payment-system.md --output-dir=stories/payment

# Link to existing planning session
/epic-to-stories docs/epics/dashboard.md --planning-session=dashboard-planning
```

### Advanced Usage
```bash
# Generate with specific sub-agent focus
/epic-to-stories epic.md --sub-agents=frontend,backend

# Generate with complexity override
/epic-to-stories epic.md --complexity=expert

# Generate with custom story prefix
/epic-to-stories epic.md --story-prefix=DASH
```

### Parameters
- `--output-dir`: Directory for generated story files (default: current directory)
- `--planning-session`: Link to existing planning session for context
- `--sub-agents`: Specific sub-agents to focus on (`frontend`, `backend`, `devops`, `testing`, `all`)
- `--complexity`: Override complexity assessment (`simple`, `medium`, `complex`, `expert`)
- `--story-prefix`: Custom prefix for story IDs (default: auto-generated)
- `--mcp-depth`: MCP analysis depth (`quick`, `standard`, `deep`, `comprehensive`)

## MCP Tool Integration

### Zen MCP Workflow
1. **Epic Analysis**: Use `thinkdeep` for comprehensive epic breakdown
   ```
   zen thinkdeep "Analyze this epic and break it down into logical, implementable stories"
   ```

2. **Story Validation**: Use `analyze` to validate story breakdown against existing codebase
   ```
   zen analyze "Assess how these stories integrate with existing system architecture"
   ```

3. **Quality Review**: Use `consensus` for multi-model validation of story breakdown
   ```
   zen consensus "Validate story breakdown quality and completeness"
   ```

### Context7 MCP Research
1. **Pattern Research**: Research implementation patterns for similar epics
   ```
   context7 resolve-library-id "authentication system"
   context7 get-library-docs "/auth0/auth0" "implementation patterns"
   ```

2. **Best Practices**: Research industry best practices for epic implementation
3. **Architecture Patterns**: Research architectural patterns relevant to epic scope

### Story Generation MCP Pipeline
1. **Requirements Analysis** → Zen thinkdeep
2. **Pattern Research** → Context7 research
3. **Story Breakdown** → Zen analyze + consensus
4. **Implementation Guidance** → Context7 documentation
5. **Quality Validation** → Zen consensus

## Generated Story Structure

### Story File Template Integration
Generated stories follow the existing PIB story template:

```markdown
# Story {epic-num}.{story-num}: {Generated Title}

## Status: Draft

## Story
- As a [user-role]
- I want [generated-functionality]
- so that [generated-benefit]

## Acceptance Criteria (ACs)
{Generated acceptance criteria based on epic requirements}

## Tasks / Subtasks
{Generated task breakdown with sub-agent assignments}

## Sub-Agent Assignment Strategy
{Automatic sub-agent assignment based on story characteristics}

## Dev Technical Guidance
{Generated technical guidance based on MCP research}
```

### Sub-Agent Assignment Logic
Stories are automatically assigned to sub-agents based on:

- **Frontend Sub-Agent**: UI/UX, user interaction, client-side logic
- **Backend Sub-Agent**: API endpoints, business logic, data processing
- **DevOps Sub-Agent**: Infrastructure, deployment, configuration
- **Testing Sub-Agent**: Test strategy, automation, validation

### Quality Criteria Generation
Each story includes:
- **Specific Acceptance Criteria**: Based on epic requirements
- **Technical Requirements**: Derived from epic technical constraints
- **LEVER Compliance**: Application of LEVER framework to story implementation
- **Integration Points**: Dependencies and integration requirements

## File Generation and Management

### Generated Files Structure
```
{output-dir}/
├── {epic-name}-{story-1}.md         # Individual story files
├── {epic-name}-{story-2}.md
├── {epic-name}-{story-3}.md
└── generation-results/
    ├── story-generation-results.md  # Generation summary and analysis
    ├── story-breakdown-analysis.md  # MCP analysis results
    └── sub-agent-assignments.md     # Sub-agent assignment details
```

### Planning Integration Files
```
.claude/plans/active/
├── {epic-name}-story-generation.md  # Generated using story-generation template
└── planning-index.json              # Updated with story entries
```

### Story Naming Convention
- **Format**: `{epic-prefix}-{sequence-number}.md`
- **Example**: `auth-1.md`, `auth-2.md`, `pay-1.md`, `pay-2.md`
- **Epic Prefix**: Auto-generated from epic name or custom `--story-prefix`

## Integration with Existing Workflows

### Planning Session Integration
- **Context Continuity**: Links to existing planning sessions for context preservation
- **Decision Inheritance**: Inherits planning decisions and architectural choices
- **Quality Standards**: Applies planning-defined quality standards to stories

### Agent Orchestration Integration
- **Sub-Agent Preparation**: Prepares stories for immediate sub-agent assignment
- **Workflow Integration**: Stories ready for existing Dev→Reviewer→Changer workflow
- **Context Enhancement**: Stories include context for enhanced agent performance

### Hook System Integration
- **Generation Hooks**: Triggers for automatic story generation in workflows
- **Context Injection**: Story context automatically injected into agent contexts
- **Progress Tracking**: Story generation tracked through existing hook system

## Quality Assurance Framework

### Story Quality Validation
- **Completeness**: All epic requirements covered by generated stories
- **Clarity**: Each story has clear, actionable requirements
- **Testability**: Acceptance criteria are specific and testable
- **Implementability**: Stories are sized appropriately for implementation

### LEVER Compliance Validation
- **Leverage**: Stories leverage existing functionality where appropriate
- **Extension**: Stories extend existing components rather than duplicating
- **Verification**: Stories include appropriate verification strategies
- **Elimination**: No duplicate functionality across stories
- **Reduction**: Story complexity minimized while maintaining functionality

### Generation Quality Metrics
- **Coverage Score**: Percentage of epic requirements covered
- **Story Coherence**: Quality of story breakdown and organization
- **Sub-Agent Balance**: Distribution of work across sub-agents
- **Dependency Clarity**: Quality of dependency identification and mapping

## Examples

### Simple Epic Generation
```bash
/epic-to-stories docs/epics/user-profile.md
```
- Generates 3-5 stories for user profile functionality
- Assigns stories to frontend and backend sub-agents
- Creates basic story files with acceptance criteria
- Updates planning index with story information

### Complex Epic with Planning Integration
```bash
/epic-to-stories docs/epics/payment-system.md --planning-session=payment-planning --complexity=expert
```
- Comprehensive analysis using multiple MCP tools
- Complex story breakdown with parallel execution planning
- Advanced sub-agent coordination requirements
- Integration with existing payment planning session

### Focused Sub-Agent Generation
```bash
/epic-to-stories docs/epics/api-redesign.md --sub-agents=backend --output-dir=backend-stories
```
- Focuses story generation on backend implementation
- Generates backend-specific stories and tasks
- Optimizes for backend sub-agent execution
- Places stories in dedicated backend directory

## Error Handling and Validation

### Epic File Validation
- **File Format**: Validates epic file format and structure
- **Content Completeness**: Ensures epic has sufficient detail for story generation
- **Requirement Clarity**: Validates that epic requirements are clear and specific

### Generation Quality Checks
- **Story Coverage**: Validates that all epic requirements are covered
- **Story Quality**: Ensures generated stories meet PIB quality standards
- **Sub-Agent Balance**: Validates reasonable distribution across sub-agents
- **Dependency Consistency**: Ensures story dependencies are logical and achievable

### Planning Integration Validation
- **Planning Session Link**: Validates planning session exists and is accessible
- **Context Consistency**: Ensures generated stories align with planning decisions
- **Index Updates**: Validates planning index updates are successful

## Related Commands
- `/planning-session` - Create planning session before story generation
- `/orchestration-plan` - Create orchestration plan from generated stories
- `/dev` - Begin development with story context
- `/sub-agent-coordination` - Coordinate sub-agents for story implementation

---
*Part of the PIB Method's automated story generation system*