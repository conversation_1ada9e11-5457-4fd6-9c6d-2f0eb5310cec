# List Active Feature Worktrees

Shows all active feature worktrees with their status, branches, and activity information.

## Usage
```
/feature-list [format]
```

## Parameters
- `format`: Display format - 'table' (default), 'detailed', or 'json'

## What This Command Shows

### Basic Information
- **Feature Name**: Clean feature identifier
- **Directory**: Full path to worktree
- **Branch**: Git branch name with timestamp
- **Status**: Current working state (Clean/Modified)

### Activity Details
- **Last Commit**: Time since last commit
- **Uncommitted Changes**: Staged/unstaged modifications
- **State Health**: Isolated state validation status
- **Context Status**: PIB Context Engineering activity

## Display Formats

### Table Format (Default)
```
| Feature | Directory | Branch | Status |
|---------|-----------|--------|--------|
| user-auth | ../pib-user-auth | feature/user-auth-20250704 | Modified |
| payment-sys | ../pib-payment-sys | feature/payment-sys-20250704 | Clean |
```

### Detailed Format
```
🌳 Active PIB Worktrees (2 found)

📁 user-auth
   Directory: /Projects/pib-user-auth
   Branch: feature/user-auth-20250704
   Status: Modified (3 files changed)
   Last Activity: 2 hours ago
   State: Healthy (isolated)
   Context: 5 evolution chains, 12 tool selections

📁 payment-sys
   Directory: /Projects/pib-payment-sys  
   Branch: feature/payment-sys-20250704
   Status: Clean
   Last Activity: 1 day ago
   State: Healthy (isolated)
   Context: 2 evolution chains, 8 tool selections
```

### JSON Format
```json
{
  "active_worktrees": {
    "user-auth": {
      "directory": "/Projects/pib-user-auth",
      "branch": "feature/user-auth-20250704", 
      "status": "modified",
      "last_commit": "2 hours ago",
      "state_health": "healthy",
      "context_chains": 5
    }
  }
}
```

## Status Indicators

### Working Status
- **Clean**: No uncommitted changes
- **Modified**: Has uncommitted changes (staged or unstaged)
- **Conflict**: Merge conflicts present
- **Detached**: Not on expected branch

### State Health
- **Healthy**: State isolation working properly
- **Warning**: Minor state issues detected
- **Error**: State corruption or missing directories

### Activity Levels
- **Active**: Recent commits (< 24 hours)
- **Idle**: No recent activity (1-7 days)
- **Stale**: Long inactivity (> 7 days)

## Examples

### Basic Listing
```
/feature-list
```
Shows table view of all active worktrees

### Detailed Information
```
/feature-list detailed
```
Shows comprehensive information about each worktree

### Machine-Readable Output
```
/feature-list json
```
Outputs JSON for scripting or integration

## Worktree Information Details

### Git Information
- **Branch tracking**: Remote branch relationship
- **Commit status**: Ahead/behind remote
- **Working tree**: Clean or modified state
- **Stash status**: Stashed changes present

### PIB State Information
- **Context isolation**: State directory health
- **Evolution chains**: Active context evolution
- **Tool selections**: JIT tool selection cache
- **MCP conversations**: Active conversation states

### File System Information
- **Directory existence**: Worktree path validation
- **Disk usage**: Worktree size information
- **Last access**: File system activity
- **Permissions**: Access and execution rights

## Integration with Other Commands

### Starting New Features
```bash
/feature-list                    # See current worktrees
/feature-start new-feature      # Start additional feature
/feature-list                   # Verify new worktree created
```

### Managing Active Features
```bash
/feature-list detailed          # Check status of all features
cd ../pib-feature-name         # Switch to specific feature
/feature-merge                  # Merge when ready
/feature-list                   # Verify merge completed
```

### Cleanup Workflow
```bash
/feature-list                   # Identify completed features
/feature-cleanup old-feature    # Remove completed worktree
/feature-list                   # Confirm cleanup
```

## Troubleshooting

### "No active worktrees found"
- This is normal if no features are in development
- Use `/feature-start <name>` to create your first worktree
- Check if you're in the correct main repository

### Missing Worktrees in List
- Worktree directory may have been manually deleted
- Use `/feature-cleanup` to clean orphaned references
- Check Git worktree list: `git worktree list`

### State Health Warnings
- Run state validation: `/.claude/hooks/state-isolator.sh validate`
- Check state backup availability
- Consider re-initializing state if corrupted

## Advanced Usage

### Filtering and Sorting
While not built-in, you can combine with shell tools:
```bash
# Show only modified worktrees
/feature-list | grep Modified

# Count active worktrees  
/feature-list json | jq '.active_worktrees | length'

# List worktrees by activity
/feature-list detailed | grep "Last Activity"
```

### Integration with Scripts
```bash
#!/bin/bash
# Example: Check for stale worktrees

worktrees=$(/feature-list json)
stale_count=$(echo "$worktrees" | jq '[.active_worktrees[] | select(.last_activity | contains("day"))] | length')

if [ "$stale_count" -gt 0 ]; then
    echo "Found $stale_count stale worktree(s)"
    echo "Consider cleaning up inactive features"
fi
```

### Monitoring Workflow
```bash
# Check status before starting work
/feature-list detailed

# Quick status check
/feature-list | grep -E "Modified|Conflict"

# Daily cleanup check
/feature-list detailed | grep -E "day|week"
```

## Performance Considerations

### Large Numbers of Worktrees
- Command remains fast with dozens of worktrees
- JSON format is most efficient for many worktrees
- Consider cleanup of completed features regularly

### Network Operations
- No network calls required for listing
- Local Git and file system operations only
- Works offline and with private repositories

### Resource Usage
- Minimal memory footprint
- Fast execution (< 1 second typically)
- Scales well with project size

## Related Commands
- `/feature-start` - Create new feature worktree
- `/feature-merge` - Integrate feature changes
- `/feature-cleanup` - Remove completed worktrees

## Implementation Notes

This command uses:
- Git worktree list for active worktree detection
- File system checks for directory validation
- PIB state inspection for health reporting
- JSON processing for structured output

---
**Note**: This command provides comprehensive visibility into your parallel development workflow and integrates with the PIB Context Engineering system for enhanced project management.