#!/bin/bash

# Feature Cleanup Command Handler
# Removes completed feature worktrees and archives their state

set -euo pipefail

# Find project root dynamically
find_project_root() {
    local dir="$(pwd)"
    while [ "$dir" != "/" ]; do
        if [ -d "$dir/.claude" ]; then
            echo "$dir"
            return 0
        fi
        dir=$(dirname "$dir")
    done
    return 1
}

PROJECT_ROOT=$(find_project_root)
if [ -z "$PROJECT_ROOT" ]; then
    echo "ERROR: Could not find project root with .claude directory" >&2
    exit 1
fi

# Handle different argument patterns
if [[ $# -eq 0 ]]; then
    # Interactive mode - show list and let user choose
    echo "🧹 Feature Cleanup - Select worktree to remove:"
    echo ""
    "$PROJECT_ROOT/.claude/hooks/worktree-manager.sh" list
    echo ""
    echo "Use: /feature-cleanup <feature-name> to remove a specific worktree"
    echo "Use: /feature-cleanup --all-stale to remove all stale worktrees"
    exit 0
elif [[ "$1" == "--all-stale" ]]; then
    # Cleanup orphaned worktrees
    "$PROJECT_ROOT/.claude/hooks/worktree-manager.sh" cleanup
elif [[ "$1" == "--dry-run" ]]; then
    echo "🔍 Dry run mode - would remove:"
    "$PROJECT_ROOT/.claude/hooks/worktree-manager.sh" list
elif [[ "$1" == "--force" ]] && [[ $# -ge 2 ]]; then
    # Force cleanup with feature name
    "$PROJECT_ROOT/.claude/hooks/worktree-manager.sh" remove "$2" "true"
else
    # Regular cleanup with feature name
    FEATURE_NAME="$1"
    FORCE="${2:-false}"
    
    # Convert --force flag
    if [[ "$FORCE" == "--force" ]]; then
        FORCE="true"
    fi
    
    "$PROJECT_ROOT/.claude/hooks/worktree-manager.sh" remove "$FEATURE_NAME" "$FORCE"
fi