#!/bin/bash

# Feature Start Command Handler
# Creates a new Git worktree with isolated state for parallel feature development

set -euo pipefail

# Find project root dynamically
find_project_root() {
    local dir="$(pwd)"
    while [ "$dir" != "/" ]; do
        if [ -d "$dir/.claude" ]; then
            echo "$dir"
            return 0
        fi
        dir=$(dirname "$dir")
    done
    return 1
}

PROJECT_ROOT=$(find_project_root)
if [ -z "$PROJECT_ROOT" ]; then
    echo "ERROR: Could not find project root with .claude directory" >&2
    exit 1
fi

# Get feature name from arguments
FEATURE_NAME="$1"
BASE_BRANCH="${2:-main}"

if [ -z "$FEATURE_NAME" ]; then
    echo "ERROR: Feature name is required" >&2
    echo "Usage: /feature-start <feature-name> [base-branch]" >&2
    exit 1
fi

# Call the worktree manager with dynamic path
"$PROJECT_ROOT/.claude/hooks/worktree-manager.sh" create "$FEATURE_NAME" "$BASE_BRANCH"