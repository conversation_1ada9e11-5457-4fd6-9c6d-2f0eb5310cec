# Merge Feature Back to Main

Merges your feature worktree changes back to the main branch with automated review and cleanup.

## Usage
```
/feature-merge [target-branch] [no-cleanup]
```

## Parameters
- `target-branch`: Branch to merge into (defaults to 'main')
- `no-cleanup`: Skip automatic worktree cleanup (optional)

## What This Command Does

### 1. Pre-Merge Validation
- Ensures all changes are committed
- Validates you're in a feature worktree
- Checks target branch accessibility
- Backs up current state

### 2. PIB Review Integration
- Automatically runs PIB reviewer agent
- Performs code quality checks
- Validates against project standards
- Generates review feedback

### 3. Merge Process
- Switches to main repository
- Updates target branch with latest changes
- Performs no-fast-forward merge with descriptive message
- Handles merge conflicts automatically where possible

### 4. Cleanup (Optional)
- Archives worktree state for reference
- Removes worktree directory
- Updates worktree index
- Maintains clean workspace

> **Note**: For creating pull requests with automated test instructions instead of direct merging, use `/feature-pr`

## Examples

### Standard Merge
```
/feature-merge
```
Merges current feature to 'main' with automatic cleanup

### Merge to Different Branch
```
/feature-merge develop
```
Merges feature to 'develop' branch instead of 'main'

### Merge Without Cleanup
```
/feature-merge main no-cleanup
```
Keeps worktree active after successful merge

## Automatic Review Process

### Code Quality Checks
- LEVER framework compliance validation
- Coding standards enforcement
- Security best practices review
- Performance considerations

### PIB Agent Integration
- Uses feature-specific context from worktree
- Leverages isolated Context Engineering data
- Applies project-specific review criteria
- Generates comprehensive feedback

### Merge Conflict Resolution
- Intelligent conflict detection
- Automated resolution where safe
- Clear guidance for manual resolution
- Rollback capability if issues arise

## Safety Features

### Pre-Merge Backup
- Complete state backup before merge
- Worktree archive creation
- Recovery instructions if needed
- Rollback to pre-merge state

### Validation Checks
- Uncommitted changes detection
- Branch synchronization verification
- Merge conflict prevention
- Target branch accessibility

### Error Recovery
- Automatic rollback on merge failures
- Clear error messages with solutions
- State restoration capabilities
- Manual resolution guidance

## Merge Message Format

Automated merge messages include:
```
feat: Merge <feature-name>

Merged from worktree: <branch-name>
Feature: <feature-description>

🤖 Generated with Claude Code

Co-Authored-By: Claude <<EMAIL>>
```

## Troubleshooting

### "Please commit all changes before merging"
```bash
git add .
git commit -m "Final changes for feature"
/feature-merge
```

### "Merge failed. Please resolve conflicts manually"
1. Check Git status for conflict files
2. Resolve conflicts in each file
3. Add resolved files: `git add <file>`
4. Complete merge: `git commit`
5. Run `/feature-cleanup <feature-name>` if needed

### "Must be in a worktree to merge"
- This command must be run from within your feature worktree
- Navigate to your worktree directory first
- Use `/feature-list` to see active worktrees

## Advanced Usage

### Custom Target Branches
```bash
# Merge to develop branch
/feature-merge develop

# Merge to release branch
/feature-merge release/v2.0

# Merge to feature integration branch
/feature-merge feature/integration
```

### Keeping Worktree for Additional Work
```bash
# Merge but keep worktree active
/feature-merge main no-cleanup

# Continue development in same worktree
# ... make more changes ...

# Merge additional changes
/feature-merge main
```

### Multiple Merge Strategy
```bash
# Feature A worktree
/feature-merge develop no-cleanup

# Feature B worktree
/feature-merge develop no-cleanup

# Both features now in develop
# Later merge develop to main
```

## Integration with PIB Workflows

### Context Preservation
- Feature-specific context is archived
- Implementation patterns are learned
- Quality metrics are recorded
- Integration success patterns saved

### Agent Learning
- Review feedback improves future suggestions
- Merge patterns enhance agent capabilities
- Context Engineering learns from outcomes
- Tool selection effectiveness measured

### Quality Metrics
- Merge success rates tracked
- Review feedback analysis
- Integration conflict patterns
- Performance impact measurement

## Post-Merge Actions

### Automatic
- Worktree state archived (if cleanup enabled)
- Git worktree removed (if cleanup enabled)
- Main repository state updated
- Success metrics recorded

### Manual Follow-up
- Test merged changes in main branch
- Update documentation if needed
- Deploy if using CI/CD
- Communicate changes to team

## Related Commands
- `/feature-start` - Create new feature worktree
- `/feature-list` - Show active worktrees and status
- `/feature-cleanup` - Manual cleanup of worktrees
- `/feature-pr` - Create pull request with test instructions

## Error Recovery

### If Merge Fails
1. **Check merge status**: `git status`
2. **Resolve conflicts**: Edit conflicted files
3. **Stage resolved files**: `git add <files>`
4. **Complete merge**: `git commit`
5. **Verify success**: `git log --oneline -5`

### If State Gets Corrupted
1. **Check backup location**: `.claude/state/backups/`
2. **Use state restoration**: `/.claude/hooks/state-isolator.sh restore <backup>`
3. **Restart from known good state**

### Emergency Rollback
```bash
# If merge succeeded but caused issues
git reset --hard HEAD~1  # In main branch
/.claude/hooks/state-isolator.sh restore <backup>
```

---
**Note**: This command integrates deeply with PIB Context Engineering and provides automated review capabilities. Always ensure changes are committed before merging.