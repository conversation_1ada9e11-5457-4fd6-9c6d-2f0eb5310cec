---
description: Create persistent epic planning sessions with cross-session context preservation
---

# Planning Session - Persistent Epic Planning

**Usage**: `/planning-session $ARGUMENTS`

Create a comprehensive planning session for epic: $ARGUMENTS

*planner epic-name=$ARGUMENTS model=gemini-2.5-pro thinking_mode=high step=1 step_number=1 total_steps=5 next_step_required=true findings="Starting persistent planning session for epic: $ARGUMENTS. This session will create comprehensive planning documentation that survives chat restarts and provides context for sub-agent coordination."

## What This Command Does

### 1. Persistent Planning Session Creation
- Creates dedicated planning session file in `.claude/plans/active/`
- Generates unique session ID for tracking
- Establishes cross-session context preservation
- Integrates with existing PIB planning infrastructure

### 2. MCP-Enhanced Epic Analysis
- **Zen MCP Integration**: Uses thinkdeep for comprehensive epic analysis
- **Context7 MCP Research**: Researches implementation patterns and best practices
- **Multi-Model Validation**: Validates planning decisions across AI models
- **Pattern Recognition**: Identifies successful planning patterns from past sessions

### 3. LEVER Framework Integration
- **Leverage Analysis**: Identifies existing functionality to reuse
- **Extension Opportunities**: Maps components that can be extended
- **Verification Strategy**: Defines validation and testing approaches
- **Elimination Planning**: Identifies duplication to remove
- **Reduction Strategy**: Plans complexity reduction opportunities

### 4. Sub-Agent Coordination Planning
- **Story Categorization**: Analyzes story breakdown for sub-agent assignment
- **Parallel Execution Planning**: Identifies parallel execution opportunities
- **Dependency Mapping**: Maps story dependencies and sequencing
- **Resource Allocation**: Plans optimal sub-agent resource allocation

## Implementation Process

### Phase 1: Epic Context Analysis
```
User Request → Epic File Analysis → MCP Analysis → LEVER Assessment → Context Creation
```

1. **Epic Goal Extraction**: Parse epic objectives and success criteria
2. **Complexity Assessment**: Evaluate technical and business complexity
3. **Zen MCP Analysis**: Deep thinking analysis of epic scope and approach
4. **Context7 Research**: Research similar implementations and patterns
5. **LEVER Evaluation**: Apply LEVER framework to planning decisions

### Phase 2: Story Breakdown Planning
```
Epic Analysis → Story Identification → Sub-Agent Mapping → Dependency Analysis → Sequencing Strategy
```

1. **Story Identification**: Break epic into logical story components
2. **Priority Assessment**: Evaluate story priority and complexity
3. **Sub-Agent Assignment**: Map stories to appropriate sub-agents
4. **Dependency Mapping**: Identify story dependencies and integration points
5. **Execution Sequencing**: Plan optimal execution sequence

### Phase 3: Orchestration Strategy
```
Story Plan → Resource Planning → Quality Gates → Risk Assessment → Timeline Planning
```

1. **Resource Allocation**: Plan James's time across sub-agents
2. **Quality Gate Definition**: Define quality checkpoints and validation
3. **Risk Assessment**: Identify technical and timeline risks
4. **Mitigation Planning**: Develop risk mitigation strategies
5. **Timeline Estimation**: Estimate realistic timeline and milestones

### Phase 4: Context Bridge Creation
```
Planning Session → Context Extraction → Bridge Documentation → Cross-Session Setup
```

1. **Context Preservation**: Extract key context for cross-session continuity
2. **Bridge Documentation**: Create context bridge document
3. **Index Registration**: Register planning session in planning index
4. **Integration Setup**: Prepare context injection for agents

## Command Parameters and Options

### Basic Usage
```bash
# Start new planning session
/planning-session user-authentication

# Resume existing planning session
/planning-session user-authentication --mode=resume

# Review completed planning session
/planning-session user-authentication --mode=review
```

### Advanced Usage
```bash
# Planning session with specific epic file
/planning-session payment-system --epic-file=docs/epics/payment-epic.md

# Planning session with specific complexity
/planning-session real-time-chat --complexity=expert

# Planning session with MCP focus
/planning-session data-migration --mcp-focus=research
```

### Parameters
- `--epic-file`: Specify existing epic file to base planning on
- `--mode`: `new` (default), `resume`, or `review`
- `--complexity`: `simple`, `medium`, `complex`, `expert` (auto-detected if not specified)
- `--mcp-focus`: `analysis`, `research`, `validation`, `comprehensive` (default: comprehensive)
- `--sub-agents`: `auto` (default), `frontend`, `backend`, `devops`, `testing`, `all`

## MCP Tool Integration

### Zen MCP Usage
- **thinkdeep**: Comprehensive epic analysis and story breakdown
- **analyze**: Existing codebase analysis for LEVER opportunities
- **chat**: Collaborative planning discussions and decision validation
- **consensus**: Multi-model validation of planning decisions

### Context7 MCP Usage
- **resolve-library-id**: Identify relevant libraries and frameworks
- **get-library-docs**: Research implementation patterns and best practices
- **Documentation Research**: Study similar implementations and architectural patterns

### Planning-Specific MCP Workflow
1. **Epic Analysis**: Use Zen thinkdeep for comprehensive epic breakdown
2. **Pattern Research**: Use Context7 to research implementation patterns
3. **Decision Validation**: Use Zen consensus for planning decision validation
4. **Existing Code Analysis**: Use Zen analyze for LEVER opportunity identification

## File Generation and Structure

### Generated Files
```
.claude/plans/active/
├── {epic-name}-planning-session.md     # Main planning session document
├── {epic-name}-context-bridge.md       # Cross-session context preservation
└── planning-session-{timestamp}.log    # Planning session activity log
```

### Planning Index Integration
- **Session Registration**: Register session in `.claude/plans/planning-index.json`
- **Status Tracking**: Track planning session status and progress
- **Relationship Mapping**: Map relationships to generated stories and orchestration plans

## Integration with Existing Workflows

### Context Engineering Integration
- **Enhanced Context Packages**: Planning context injected into agent contexts
- **Relevance Scoring**: Planning decisions weighted in context relevance
- **Pattern Learning**: Successful planning patterns captured for future sessions

### Agent Orchestration Integration
- **Workflow State**: Planning sessions tracked in workflow state management
- **Agent Assignment**: Planning decisions feed into agent assignment strategies
- **Quality Gates**: Planning quality gates integrated with existing review processes

### Hook System Integration
- **Planning Hooks**: Automatic planning context injection when agents start work
- **Progress Tracking**: Planning session progress tracked through hook system
- **Context Evolution**: Planning context evolves as implementation progresses

## Quality Assurance Framework

### Planning Quality Gates
- **Epic Clarity**: Epic goals and success criteria clearly defined
- **Story Completeness**: Epic fully broken down into implementable stories
- **LEVER Compliance**: Planning follows LEVER framework principles
- **Sub-Agent Optimization**: Optimal sub-agent assignment and coordination

### Validation Checklist
- [ ] Epic goals clearly defined and measurable
- [ ] Story breakdown covers complete epic scope
- [ ] Sub-agent assignments are appropriate and balanced
- [ ] Dependencies identified and sequenced properly
- [ ] LEVER framework applied to planning decisions
- [ ] Risk assessment completed with mitigation strategies
- [ ] Timeline realistic and achievable
- [ ] Context bridge established for cross-session continuity

## Examples

### Simple Feature Planning
```bash
/planning-session password-reset
```
- Analyzes simple feature epic
- Creates basic story breakdown
- Plans single sub-agent implementation
- Establishes simple orchestration

### Complex System Planning
```bash
/planning-session real-time-messaging --complexity=expert --mcp-focus=comprehensive
```
- Comprehensive analysis with multiple MCP tools
- Complex story breakdown with parallel execution
- Multi-sub-agent coordination planning
- Advanced risk assessment and mitigation

### Planning Session Resume
```bash
/planning-session user-dashboard --mode=resume
```
- Resumes existing planning session
- Loads previous context and decisions
- Continues from current planning phase
- Preserves all previous analysis and decisions

## Related Commands
- `/epic-to-stories` - Generate stories from planning session
- `/orchestration-plan` - Create orchestration plan from planning session
- `/context-bridge` - Manage cross-session context
- `/dev` - Enhanced development with planning context

---
*Part of the PIB Method's persistent planning system*