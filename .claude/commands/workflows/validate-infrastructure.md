---
description: Validate infrastructure design and implementation using Architect expertise for feasibility assessment
---

# PIB Validate Infrastructure Command

**Usage**: `*validate-infrastructure [scope] [options]`

## Purpose

Validates infrastructure design and implementation using Architect expertise, leveraging PIB principles to verify infrastructure feasibility while extending validation capabilities for comprehensive assessment.

### Key Benefits
- Leverages existing infrastructure architecture and design documentation
- Extends current validation capabilities with comprehensive feasibility assessment
- Reduces infrastructure implementation risk through systematic validation
- Integrates seamlessly with PIB infrastructure planning and deployment workflows

### When to Use
- After infrastructure architecture or deployment plans are created
- Before implementing infrastructure changes or new deployments
- As part of infrastructure design validation and risk assessment
- When evaluating infrastructure feasibility for new requirements

## Implementation

### Step-by-Step Algorithm

#### 1. LEVER Framework Pre-Validation
Before executing, validate these principles:
- **L** - Leverage: Existing infrastructure documentation, validation frameworks, and assessment tools
- **E** - Extend: Current validation capabilities with comprehensive feasibility assessment
- **V** - Verify: Infrastructure designs against technical constraints and operational requirements
- **E** - Eliminate: Infrastructure designs that don't meet feasibility or operational criteria
- **R** - Reduce: Infrastructure validation complexity while maximizing assessment accuracy

#### 2. Context Gathering
```bash
# Load infrastructure and validation context
CURRENT_AGENT=$(cat .claude/current-workflow-state.json | jq -r '.currentAgent // "architect"')
PROJECT_CONTEXT=$(cat .ai/project-context.md 2>/dev/null || echo "No project context available")
INFRASTRUCTURE_ARCH=$(cat docs/infrastructure-architecture.md 2>/dev/null || echo "No infrastructure architecture found")
DEPLOYMENT_PLAN=$(cat docs/deployment-plan.md 2>/dev/null || echo "No deployment plan found")
INFRASTRUCTURE_REVIEW=$(cat docs/infrastructure-review.md 2>/dev/null || echo "No infrastructure review found")
```

#### 3. Agent-Specific Processing
Adapt behavior based on active agent persona:

##### If Architect Active
- Focus on comprehensive infrastructure design validation and feasibility assessment
- Technical constraint analysis and architectural compliance verification
- Integration validation and system compatibility assessment

##### If DevOps Active  
- Emphasize operational infrastructure validation and deployment feasibility
- Automation validation and infrastructure as code assessment
- Monitoring and operational procedures validation

#### 4. Core Processing
- Load Architect persona and infrastructure validation task
- Analyze infrastructure design against technical and operational constraints
- Create comprehensive infrastructure validation using established framework
- Document validation results, constraint violations, and feasibility recommendations
- Generate infrastructure implementation readiness assessment and risk mitigation

#### 5. Output Generation
- Structured infrastructure validation saved to `docs/infrastructure-validation.md`
- Feasibility assessment matrix and constraint compliance documentation
- Risk assessment and mitigation strategy recommendations
- Implementation readiness checklist and validation criteria

#### 6. Workflow Integration
- Updates workflow state to indicate infrastructure validation completion
- Prepares context for infrastructure implementation and deployment execution
- Integrates with deployment planning and infrastructure management workflows

## Quality Features / Validation Checklist

### PIB Compliance Validation
- [ ] **LEVER Principles**: Leverages existing infrastructure documentation and validation frameworks
- [ ] **Agent Alignment**: Uses Architect or DevOps infrastructure validation expertise
- [ ] **Workflow Integration**: Properly follows infrastructure validation workflow
- [ ] **Knowledge Capture**: Documents validation findings and feasibility assessment
- [ ] **Context Awareness**: Builds upon existing infrastructure and operational context

### Technical Quality Gates
- [ ] **Input Validation**: Handles missing infrastructure documentation gracefully
- [ ] **Error Handling**: Provides guidance when validation data is incomplete
- [ ] **Performance**: Efficient infrastructure validation and assessment process
- [ ] **Consistency**: Maintains coherent validation criteria and assessment methodology
- [ ] **Documentation**: Clear, actionable infrastructure validation results

### Output Quality Assurance
- [ ] **Completeness**: Covers all infrastructure components and validation criteria
- [ ] **Accuracy**: Infrastructure validation is based on current technical constraints
- [ ] **Relevance**: Validation aligns with operational requirements and implementation goals
- [ ] **Actionability**: Provides specific infrastructure implementation guidance and risk mitigation
- [ ] **Integration**: Seamlessly connects with existing infrastructure and deployment documentation

### Knowledge Management Standards
- [ ] **Persistence**: Saves infrastructure validation to `docs/infrastructure-validation.md`
- [ ] **Versioning**: Maintains infrastructure validation history and assessment tracking
- [ ] **Cross-Reference**: Links to infrastructure architecture and deployment documentation
- [ ] **Searchability**: Structured for easy reference during implementation planning
- [ ] **Agent Context**: Updates Architect and operations team validation knowledge

## Integration

### Workflow Hooks
- **Triggers**: `read-workflow.sh` for LEVER principles reminder
- **Updates**: `workflow-transition.sh` for infrastructure validation completion
- **Notifications**: `notification-hook.sh` for infrastructure validation alerts
- **Context**: Updates `.claude/current-workflow-state.json`

### Knowledge Management Integration
- **Saves**: Infrastructure validation to `docs/infrastructure-validation.md`
- **Updates**: Project context with infrastructure validation status in `.ai/project-context.md`
- **Creates**: Infrastructure validation tracking in `.ai/infrastructure-validation/` directory
- **Links**: Cross-references with infrastructure architecture and deployment documentation

### Agent Context Updates
- **Architect**: Updates infrastructure validation knowledge and feasibility assessment capabilities
- **All Agents**: Updates shared project infrastructure validation status and implementation readiness
- **DevOps**: Provides operational infrastructure validation and deployment readiness assessment
- **Platform Engineer**: Provides platform-level infrastructure validation and implementation guidance

### Follow-up Command Preparation
Results prepare context for:
- `*create-deployment-plan` - Deployment planning based on validation results
- `*platform-change-management` - Infrastructure implementation based on validation
- `*review-infrastructure` - Infrastructure review and optimization based on validation findings
- `*update-knowledge` - Distribute infrastructure validation results to all agents

## Related Commands

### Core Infrastructure Commands
- `*create-infrastructure-architecture` - Infrastructure architecture for validation
- `*review-infrastructure` - Infrastructure review and assessment
- `*create-deployment-plan` - Deployment planning based on validation

### Workflow Commands
- `*platform-change-management` - Infrastructure implementation and change management
- `*plan-workflow` - Infrastructure implementation workflow planning
- `*correct-course` - Strategic adjustments based on validation findings

### Agent Commands
- `/switch-agent architect` - Optimal agent for comprehensive infrastructure validation
- `/switch-agent devops` - Operational infrastructure validation focus

### Knowledge Commands
- `/update-knowledge` - Distribute infrastructure validation results to all agents
- `/memory-extract` - Extract infrastructure validation insights and assessment data

## Example Usage

### Basic Usage
```bash
# Validate current project infrastructure design
*validate-infrastructure
```

### Advanced Usage
```bash
# Validate specific infrastructure components with detailed assessment
*validate-infrastructure networking,storage --detailed-constraints --risk-assessment
```

### Agent-Specific Usage
```bash
# When Architect agent is active:
*validate-infrastructure --comprehensive --technical-constraints

# When DevOps agent is active:
*validate-infrastructure --operational --deployment-readiness
```

### Workflow Integration Example
```bash
# Part of infrastructure implementation workflow:
/create-infrastructure-architecture
/review-infrastructure
*validate-infrastructure  # ← Comprehensive infrastructure validation
/create-deployment-plan  # Deployment based on validation
/platform-change-management  # Implementation
```

## Notes

### Agent Personas
This command adapts its behavior based on the active agent:
- **Architect**: Comprehensive infrastructure design validation with technical feasibility focus
- **DevOps**: Operational infrastructure validation with deployment and automation focus
- **Platform Engineer**: Platform-level infrastructure validation with scalability and self-service focus
- **Security**: Security-focused infrastructure validation with compliance and threat assessment
- **QA**: Infrastructure testing and validation procedures assessment

### LEVER Framework Emphasis
Every execution must demonstrate:
- **Leverage**: Existing infrastructure documentation, validation frameworks, and technical constraints
- **Extend**: Current validation capabilities with comprehensive feasibility and risk assessment
- **Verify**: Infrastructure designs against operational requirements, technical constraints, and best practices
- **Eliminate**: Infrastructure designs that don't meet feasibility, security, or operational criteria
- **Reduce**: Infrastructure validation complexity while maximizing assessment accuracy and implementation confidence

### Best Practices
- Include both technical feasibility and operational readiness in validation
- Document all constraint violations and provide mitigation strategies
- Validate infrastructure against current and projected operational requirements
- Include risk assessment and implementation readiness criteria in validation results