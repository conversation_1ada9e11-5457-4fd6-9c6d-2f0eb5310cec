# Create Pull Request from Feature Worktree

Creates a GitHub pull request from your feature worktree with automated test instructions based on Playwright workflows.

## Usage
```
/feature-pr [target-branch] [--no-tests]
```

## Parameters
- `target-branch`: Branch to create PR against (defaults to 'main')
- `--no-tests`: Skip test workflow detection and instructions

## What This Command Does

### 1. Pre-PR Validation
- Ensures all changes are committed and pushed
- Validates you're in a feature worktree
- Checks for existing PRs for the branch
- Runs automated code review via PIB agents

### 2. Test Workflow Detection
- Scans for Playwright test workflows in `.claude/state/testing/workflows/`
- Identifies relevant test scenarios based on changed files
- Extracts test commands and configurations
- Generates test execution instructions

### 3. PR Creation
- Creates comprehensive PR with:
  - Feature description from context
  - List of changes with file impacts
  - Automated test instructions
  - Manual test checklist if needed
  - PIB review feedback summary
  - Deployment considerations

### 4. Test Instructions Format
The PR body includes:
```markdown
## 🧪 Automated Testing

### Playwright Test Workflows
The following test workflows are available for this feature:

- **Login Flow**: `/test-workflow login-flow`
- **User Registration**: `/test-workflow user-registration`
- **Feature X Integration**: `/test-workflow feature-x`

### Running Tests
```bash
# Run all relevant tests
/test-workflow run-all --feature user-authentication

# Run specific test
/test-workflow login-flow

# Run in visible mode for debugging
/test-visible login-flow
```

### Manual Testing Checklist
- [ ] Test new feature in Chrome/Firefox/Safari
- [ ] Verify mobile responsiveness
- [ ] Check accessibility compliance
- [ ] Test error handling scenarios
- [ ] Verify performance metrics
```

## Examples

### Standard PR Creation
```
/feature-pr
```
Creates PR against 'main' with full test instructions

### PR to Different Branch
```
/feature-pr develop
```
Creates PR targeting 'develop' branch

### PR Without Test Detection
```
/feature-pr main --no-tests
```
Creates PR with manual test checklist only

## PR Template Structure

```markdown
## Summary
Brief description of the feature and its purpose

## Changes
- List of key changes made
- Impact on existing functionality
- New dependencies or requirements

## PIB Review Summary
[Automated review feedback from PIB agents]

## Testing Instructions
[Automated Playwright workflows and manual test steps]

## Deployment Notes
- Environment variables needed
- Database migrations required
- Configuration changes

## Checklist
- [ ] Code follows LEVER principles
- [ ] Tests are passing
- [ ] Documentation updated
- [ ] No security vulnerabilities
- [ ] Performance impact assessed

🤖 Generated with Claude Code

Co-Authored-By: Claude <<EMAIL>>
```

## Integration with Test Workflows

### Automatic Test Discovery
The command searches for:
1. Workflows in `.claude/state/testing/workflows/`
2. Test files matching changed code
3. E2E test scenarios in the codebase
4. Existing Playwright configurations

### Test Workflow Mapping
Maps feature changes to relevant tests:
- Authentication changes → Login/logout tests
- API changes → Integration tests
- UI changes → Visual regression tests
- Form changes → Validation tests

### CI/CD Integration
PR includes instructions for:
- GitHub Actions test runs
- Local test execution
- Test result reporting
- Coverage requirements

## Advanced Features

### Multi-Stage Testing
```bash
# Create PR with staged testing plan
/feature-pr main --test-stages dev,staging,prod
```

### Custom Test Templates
```bash
# Use specific test template
/feature-pr main --test-template comprehensive
```

### Test Dependencies
```bash
# Include test data setup instructions
/feature-pr main --include-test-data
```

## Safety Features

### Automatic Checks
- Uncommitted changes detection
- Branch push verification
- Existing PR detection
- Merge conflict prevention

### Quality Gates
- PIB review must pass
- Test workflows must be valid
- Security scan completion
- Performance baseline check

## Troubleshooting

### "No test workflows found"
- Check `.claude/state/testing/workflows/` directory
- Run `/test-workflow create` to generate workflows
- Use `--no-tests` flag for manual test instructions

### "Branch not pushed to remote"
```bash
git push -u origin <branch-name>
/feature-pr
```

### "PR already exists"
- Check existing PRs on GitHub
- Update existing PR instead of creating new one
- Close stale PRs before creating new ones

## Related Commands
- `/feature-start` - Create new feature worktree
- `/feature-merge` - Direct merge without PR
- `/test-workflow` - Manage Playwright test workflows
- `/test-config` - Configure test settings

## Best Practices

### Before Creating PR
1. Run all relevant tests locally
2. Update documentation
3. Check for lint errors
4. Review changes yourself first

### PR Description
- Be specific about what changed and why
- Include screenshots for UI changes
- Reference related issues or tickets
- Mention breaking changes clearly

### Test Coverage
- Ensure new code has tests
- Update existing tests if behavior changed
- Include edge case testing
- Document test assumptions

---
**Note**: This command requires GitHub CLI (`gh`) to be installed and authenticated. Test workflow detection works best when Playwright tests are properly organized.