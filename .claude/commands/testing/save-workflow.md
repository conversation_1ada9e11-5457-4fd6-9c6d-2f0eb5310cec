# Save Test Workflow

Save a complete test workflow with routes, scenarios, and test data for reuse.

## Usage
```bash
/project:save-workflow {workflow-name} --routes={route1,route2} --scenarios={scenario1,scenario2}
```

## Arguments
- `workflow-name`: Unique identifier for the saved workflow
- `--routes`: Comma-separated list of routes/URLs to test
- `--scenarios`: Optional test scenarios to include

## Implementation
Create and save comprehensive test workflows with LEVER architecture validation:

1. **LEVER Pre-Check**: Validate test workflow against LEVER principles
   - **L**everage: Search for existing test patterns and reusable components
   - **E**xtend: Identify opportunities to extend existing test workflows
   - **V**erify: Plan reactive test validation patterns
   - **E**liminate: Prevent test duplication across workflows
   - **R**educe: Optimize workflow complexity and execution time

2. **Route Collection**: Gather and validate all routes in the workflow
3. **Scenario Definition**: Define test scenarios following LEVER principles
4. **Test Data**: Collect form data, authentication requirements, and test inputs
5. **Workflow Storage**: Save the complete workflow configuration with LEVER metadata
6. **LEVER Validation**: Ensure workflow follows LEVER architecture principles

Workflow components saved:
- **Routes**: Complete list of URLs/endpoints to test
- **Test Scenarios**: Specific test cases for each route
- **Authentication**: Required credentials and login flows
- **Test Data**: Form inputs, API payloads, and test datasets
- **Assertions**: Expected outcomes and validation rules
- **Dependencies**: Route dependencies and execution order

Interactive workflow creation:
```
Creating workflow: user-registration-flow

Routes to test:
1. /signup (registration form)
2. /verify-email (email verification)
3. /welcome (post-registration dashboard)
4. /profile (user profile setup)

For each route, specify:
- Required test data
- Authentication requirements
- Expected outcomes
- Error scenarios to test
```

Workflow configuration saved:
```json
{
  "name": "user-registration-flow",
  "description": "Complete user registration and onboarding",
  "routes": [
    {
      "path": "/signup",
      "method": "GET/POST",
      "testData": {
        "email": "test-{timestamp}@example.com",
        "username": "testuser{random}",
        "password": "TestPass123!"
      },
      "scenarios": ["happy-path", "validation-errors", "duplicate-email"],
      "assertions": ["form-submission", "success-redirect", "email-sent"]
    }
  ],
  "credentials": "test-account",
  "execution": "sequential",
  "cleanup": "delete-test-users"
}
```

Workflow features:
- **Route Dependencies**: Define execution order and dependencies
- **Data Generation**: Dynamic test data generation patterns
- **Cleanup Actions**: Post-test cleanup procedures
- **Environment Config**: Different configurations for dev/staging/prod
- **Error Handling**: Recovery strategies for failed steps

Storage location: `.claude/workflows/{workflow-name}.json`

Arguments: $ARGUMENTS