# Test Login

Automated authentication using saved credentials.

## Usage
```bash
/project:test-login {site-name} --credentials={name}
```

## Arguments
- `site-name`: Target application or site for login
- `--credentials`: Name of saved credentials to use

## Implementation
Automate login process using previously saved credentials:

1. **Credential Retrieval**: Load saved credentials from secure storage
2. **Navigation**: Navigate to login page
3. **Form Automation**: Fill login forms automatically
4. **Authentication**: Handle various authentication methods
5. **Session Validation**: Verify successful login

The login process supports:
- Username/password authentication
- Email/password combinations
- Multi-factor authentication (MFA/2FA)
- OAuth and social login flows
- Remember me functionality
- Session persistence validation

Authentication methods handled:
- Standard form-based login
- OAuth providers (Google, GitHub, etc.)
- SAML and enterprise SSO
- API key authentication
- Token-based authentication

After successful login:
- Verify dashboard or authenticated page load
- Capture authentication tokens if needed
- Store session information for subsequent tests
- Validate user permissions and access levels

The command ensures reliable authentication for automated testing workflows.

Arguments: $ARGUMENTS