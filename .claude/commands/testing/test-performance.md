# Test Performance

Comprehensive performance benchmarking and analysis.

## Usage
```bash
/project:test-performance {target}
```

## Arguments
- `target`: Application URL or service to benchmark

## Implementation
Execute comprehensive performance testing and analysis:

1. **Core Web Vitals**: Measure LCP, FID, CLS, and other metrics
2. **Load Testing**: Simulate user load and measure response times
3. **Resource Analysis**: Analyze network requests and resource loading
4. **Memory Profiling**: Monitor memory usage and potential leaks
5. **Rendering Performance**: Measure paint times and frame rates

Performance metrics collected:
- First Contentful Paint (FCP)
- Largest Contentful Paint (LCP)
- First Input Delay (FID)
- Cumulative Layout Shift (CLS)
- Time to Interactive (TTI)
- Total Blocking Time (TBT)

Analysis includes:
- Page load time breakdown
- Network waterfall analysis
- JavaScript execution time
- CSS rendering performance
- Image optimization assessment
- Font loading optimization

Test scenarios:
- Cold cache performance (first visit)
- Warm cache performance (return visit)
- Slow network conditions (3G simulation)
- CPU throttling simulation
- Memory-constrained environments

Reports generated:
- Lighthouse performance audit
- WebPageTest-style waterfall charts
- Resource loading timeline
- Performance budget compliance
- Regression analysis vs previous runs
- Optimization recommendations

The testing provides actionable insights for performance improvements.

Arguments: $ARGUMENTS