# Test Headless E2E

Execute comprehensive headless end-to-end testing for CI/CD automation.

## Usage
```bash
/project:test-headless {target} --port={port}
```

## Arguments
- `target`: Application URL or service to test
- `--port`: Optional port number (default: 3000)

## Implementation
Run automated headless browser tests using <PERSON><PERSON> with the following configuration:
- Headless mode enabled for CI/CD compatibility
- Screenshot capture on failures
- Test report generation
- Performance metrics collection
- Mobile and desktop viewport testing

The target should be either a URL (e.g., https://app.example.com) or a service name that resolves to localhost with the specified port.

Test execution includes:
1. Authentication flow validation
2. Core user journey testing  
3. Form submission and validation
4. Navigation and routing verification
5. Performance benchmarking
6. Accessibility compliance checks

Arguments: $ARGUMENTS