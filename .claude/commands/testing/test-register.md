# Test Registration

Automatically register new user accounts for testing purposes.

## Usage
```bash
/project:test-register {site-name} --save-as={name}
```

## Arguments
- `site-name`: Target application or site for registration
- `--save-as`: Name to save the registered credentials under

## Implementation
Automate user registration process with unique test accounts:

1. **Dynamic User Generation**: Create unique usernames and email addresses
2. **Form Automation**: Automatically fill registration forms
3. **Email Verification**: Handle email verification flows when required
4. **Credential Storage**: Securely save generated credentials
5. **Account Verification**: Validate successful registration

The registration process includes:
- Generating unique test user data (username, email, password)
- Navigating to registration page
- Filling out registration forms
- Handling CAPTCHA and verification steps
- Confirming account creation
- Storing credentials securely for future use

Supported sites and registration flows:
- Custom application registration forms
- OAuth provider account creation
- Multi-step registration processes
- Email verification workflows

Generated credentials are saved securely and can be retrieved using the credential management commands.

Arguments: $ARGUMENTS