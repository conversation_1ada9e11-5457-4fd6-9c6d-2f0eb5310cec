---
description: Compare competitor features using Architect expertise for strategic feature analysis
---

# PIB Compare Competitor Features Command

**Usage**: `*compare-competitor-features [feature-category] [options]`

## Purpose

Compares competitor features using Architect expertise, leveraging PIB principles to analyze market feature standards while identifying opportunities to extend beyond current competitive offerings with superior architecture.

### Key Benefits
- Leverages existing competitive research and feature documentation
- Extends current feature understanding with architectural feasibility analysis
- Reduces feature development risk by understanding proven market implementations
- Integrates seamlessly with PIB enhancement planning and architectural workflows

### When to Use
- Before planning feature enhancements or new feature development
- When evaluating current feature set against market standards
- As part of product roadmap planning and competitive positioning
- When assessing technical feasibility of competitive feature implementations

## Implementation

### Step-by-Step Algorithm

#### 1. LEVER Framework Pre-Validation
Before executing, validate these principles:
- **L** - Leverage: Existing competitive analysis and feature documentation
- **E** - Extend: Current feature capabilities beyond competitive standards
- **V** - Verify: Feature insights against architectural constraints and user value
- **E** - Eliminate: Feature complexity that doesn't provide competitive advantage
- **R** - Reduce: Implementation complexity while maximizing feature differentiation

#### 2. Context Gathering
```bash
# Load project and competitive context
CURRENT_AGENT=$(cat .claude/current-workflow-state.json | jq -r '.currentAgent // "architect"')
PROJECT_CONTEXT=$(cat .ai/project-context.md 2>/dev/null || echo "No project context available")
CURRENT_ARCHITECTURE=$(cat docs/architecture.md 2>/dev/null || echo "No architecture found")
COMPETITIVE_ANALYSIS=$(cat docs/competitive-analysis.md 2>/dev/null || echo "No competitive analysis found")
CURRENT_PRD=$(cat docs/prd.md 2>/dev/null || echo "No PRD found")
```

#### 3. Agent-Specific Processing
Adapt behavior based on active agent persona:

##### If Architect Active
- Focus on technical feature architecture and implementation feasibility
- System integration analysis and architectural impact assessment
- Scalability and performance implications of competitive features

##### If PM Active  
- Emphasize business value analysis and market positioning assessment
- Feature prioritization and resource allocation for competitive features
- ROI analysis and strategic feature roadmap planning

#### 4. Core Processing
- Load Architect persona and competitor feature analysis task
- Research competitor feature implementations using available analysis tools
- Create comprehensive feature comparison using established framework
- Document feature capabilities, implementation approaches, and architectural considerations
- Generate strategic recommendations for feature enhancement and differentiation

#### 5. Output Generation
- Structured competitor feature analysis saved to `docs/competitor-feature-analysis.md`
- Feature comparison matrix with architectural feasibility assessment
- Implementation approach documentation and technical recommendations
- Strategic feature enhancement opportunities and development priorities

#### 6. Workflow Integration
- Updates workflow state to indicate competitive feature analysis completion
- Prepares context for enhancement PRD creation and architectural planning
- Integrates with product planning and technical strategy workflows

## Quality Features / Validation Checklist

### PIB Compliance Validation
- [ ] **LEVER Principles**: Builds upon existing competitive analysis and architectural knowledge
- [ ] **Agent Alignment**: Uses Architect technical expertise and feasibility analysis
- [ ] **Workflow Integration**: Properly follows competitive analysis and planning workflow
- [ ] **Knowledge Capture**: Documents feature insights and architectural considerations
- [ ] **Context Awareness**: Builds upon existing project architecture and competitive context

### Technical Quality Gates
- [ ] **Input Validation**: Handles missing competitive data or architecture specs gracefully
- [ ] **Error Handling**: Provides guidance when feature information is incomplete
- [ ] **Performance**: Efficient feature analysis and comparison process
- [ ] **Consistency**: Maintains coherent feature evaluation criteria and methodology
- [ ] **Documentation**: Clear, actionable feature insights and implementation guidance

### Output Quality Assurance
- [ ] **Completeness**: Covers major competitive features and implementation approaches
- [ ] **Accuracy**: Feature analysis is based on current and relevant competitive examples
- [ ] **Relevance**: Insights align with project architecture and business objectives
- [ ] **Actionability**: Provides specific feature enhancement and implementation strategies
- [ ] **Integration**: Seamlessly connects with existing architecture and product requirements

### Knowledge Management Standards
- [ ] **Persistence**: Saves feature analysis to `docs/competitor-feature-analysis.md`
- [ ] **Versioning**: Maintains feature insight history and competitive intelligence updates
- [ ] **Cross-Reference**: Links to competitive analysis and architecture documentation
- [ ] **Searchability**: Structured for easy reference during feature planning
- [ ] **Agent Context**: Updates Architect and product team feature knowledge

## Integration

### Workflow Hooks
- **Triggers**: `read-workflow.sh` for LEVER principles reminder
- **Updates**: `workflow-transition.sh` for competitive feature analysis completion
- **Notifications**: `notification-hook.sh` for feature analysis completion alerts
- **Context**: Updates `.claude/current-workflow-state.json`

### Knowledge Management Integration
- **Saves**: Competitor feature analysis to `docs/competitor-feature-analysis.md`
- **Updates**: Project context with feature competitive intelligence in `.ai/project-context.md`
- **Creates**: Feature research tracking in `.ai/feature-analysis/` directory
- **Links**: Cross-references with competitive analysis and architecture specifications

### Agent Context Updates
- **Architect**: Updates feature architecture knowledge and implementation strategies
- **All Agents**: Updates shared project competitive feature context
- **PM**: Provides feature-based enhancement opportunities and market positioning
- **Development**: Provides feature implementation standards and competitive benchmarks

### Follow-up Command Preparation
Results prepare context for:
- `*create-enhancement-prd` - Feature-focused enhancement requirements
- `*architect-design` - Architecture updates for competitive feature support
- `*analyze-competitor-ux` - UX analysis for identified competitive features
- `*update-knowledge` - Distribute feature competitive intelligence to all agents

## Related Commands

### Core Analysis Commands
- `*analyze-competitor-ux` - UX analysis for competitive features
- `*create-enhancement-prd` - Enhancement planning based on feature gaps
- `*architect-design` - Architecture planning for competitive features

### Workflow Commands
- `*plan-workflow` - Feature development workflow planning
- `*create-next-story` - Implementation stories for competitive features
- `*correct-course` - Strategic pivots based on feature competitive intelligence

### Agent Commands
- `/switch-agent architect` - Optimal agent for technical feature analysis
- `/switch-agent pm` - Business-focused competitive feature planning

### Knowledge Commands
- `/update-knowledge` - Distribute feature competitive intelligence to all agents
- `/memory-extract` - Extract feature insights and implementation intelligence

## Example Usage

### Basic Usage
```bash
# Compare competitor features for current project
*compare-competitor-features
```

### Advanced Usage
```bash
# Compare specific feature categories with technical focus
*compare-competitor-features "authentication,payment" --technical-deep-dive
```

### Agent-Specific Usage
```bash
# When Architect agent is active:
*compare-competitor-features --implementation-focus --scalability-analysis

# When PM agent is active:
*compare-competitor-features --business-value --market-positioning
```

### Workflow Integration Example
```bash
# Part of competitive feature enhancement workflow:
/analyst-brief  # Initial competitive landscape
*compare-competitor-features  # ← Detailed feature competitive analysis
/analyze-competitor-ux  # UX analysis for key features
/create-enhancement-prd  # Enhancement planning
/architect-design  # Architecture updates for features
```

## Notes

### Agent Personas
This command adapts its behavior based on the active agent:
- **Architect**: Technical feature analysis with implementation feasibility focus
- **PM**: Business value analysis and strategic feature roadmap planning
- **Design Architect**: UX/UI implications of competitive feature implementations
- **Developer**: Implementation complexity and technical debt considerations
- **QA**: Testing strategies and quality standards for competitive features

### LEVER Framework Emphasis
Every execution must demonstrate:
- **Leverage**: Existing competitive research, architecture documentation, and feature analysis
- **Extend**: Current feature capabilities with strategically superior implementations
- **Verify**: Feature insights against architectural constraints, user value, and business goals
- **Eliminate**: Feature complexity that doesn't provide clear competitive advantage
- **Reduce**: Implementation risk while maximizing feature differentiation and user value

### Best Practices
- Focus on features that provide clear competitive advantages and user value
- Include technical feasibility assessment for all analyzed features
- Document implementation approaches and architectural implications
- Validate feature insights against current system architecture and constraints