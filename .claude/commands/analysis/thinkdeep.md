---
description: Deep thinking and strategic analysis for complex problems using PIB methodology
---

# PIB ThinkDeep Command

**Usage**: `*thinkdeep [problem-description] [context/scope]`

## Purpose

Perform deep, strategic thinking and analysis for complex problems, design decisions, and architectural challenges using PIB principles, LEVER framework, and agent-specific expertise.

## LEVER Framework Strategic Analysis

Before deep thinking, establish strategic principles:
- **L** - Leverage: What existing knowledge, patterns, and solutions can inform our approach?
- **E** - Extend: How can we build upon existing systems and decisions rather than starting fresh?
- **V** - Verify: How can we validate our strategic thinking through existing evidence and patterns?
- **E** - Eliminate: What assumptions, complexities, and unnecessary elements can we remove?
- **R** - Reduce: What's the simplest strategic approach that achieves the desired outcome?

## Agent-Specific Strategic Focus

### Current Agent Context
Adapt strategic thinking approach based on active agent persona:

#### If Architect Agent Active
- Focus on system-wide implications and design decisions
- Analyze long-term architectural consequences and trade-offs
- Consider scalability, maintainability, and evolution paths
- Evaluate technology choices and integration strategies

#### If Analyst Agent Active
- Focus on business value and market implications
- Analyze stakeholder needs and requirement alignment
- Consider competitive advantages and differentiation
- Evaluate ROI and strategic business impact

#### If Developer Agent Active
- Focus on implementation feasibility and technical challenges
- Analyze development effort and complexity implications
- Consider maintainability and code organization impact
- Evaluate technology learning curves and team capabilities

#### If Product Manager Active
- Focus on user value and product strategy alignment
- Analyze feature prioritization and roadmap implications
- Consider market timing and competitive positioning
- Evaluate resource allocation and delivery timelines

## Deep Thinking Methodology

### 1. Problem Space Analysis
```bash
# Capture deep thinking session
echo "## Deep Thinking Session: $(date)" >> .ai/thinking-log.md
echo "### Problem: $PROBLEM_DESCRIPTION" >> .ai/thinking-log.md
echo "### Context: $CONTEXT_SCOPE" >> .ai/thinking-log.md
echo "### Agent Perspective: $CURRENT_AGENT" >> .ai/thinking-log.md
```

### 2. Multi-Dimensional Exploration

#### Strategic Dimensions
- **Business Impact**: How does this affect business goals and metrics?
- **Technical Implications**: What are the technical risks and opportunities?
- **User Experience**: How does this impact user value and satisfaction?
- **Team Dynamics**: How does this affect team productivity and capabilities?
- **Resource Requirements**: What resources (time, money, people) are needed?

#### Time Horizons
- **Immediate (0-3 months)**: Short-term implications and quick wins
- **Medium-term (3-12 months)**: Strategic positioning and capability building
- **Long-term (1-3 years)**: Vision alignment and sustainable growth
- **Future-proofing (3+ years)**: Adaptability and evolution potential

### 3. LEVER Strategic Validation

#### Leverage Analysis
- What existing solutions, patterns, or knowledge can we build upon?
- How can we leverage current team capabilities and domain expertise?
- What external resources and partnerships can we utilize?

#### Extension Opportunities
- How can we extend existing systems rather than replacing them?
- What current processes and workflows can be enhanced?
- How can we build upon existing user behaviors and expectations?

#### Verification Methods
- How can we validate our strategic assumptions with minimal risk?
- What experiments or prototypes can test our hypotheses?
- How can we measure success and gather feedback early?

#### Elimination Priorities
- What unnecessary complexity can we remove from the problem?
- Which assumptions or constraints can be challenged or eliminated?
- What scope can be reduced while maintaining core value?

#### Reduction Strategies
- What's the minimal viable approach that delivers value?
- How can we simplify the problem into manageable components?
- What's the most direct path to the desired outcome?

### 4. Alternative Solution Exploration

#### Solution Categories
- **Incremental Improvements**: Enhancing existing approaches
- **Revolutionary Changes**: Fundamentally new approaches
- **Hybrid Solutions**: Combining multiple approaches
- **No-Code/Low-Code**: Leveraging existing tools and platforms

#### Risk-Reward Analysis
- **High-Reward, Low-Risk**: Obvious opportunities to pursue
- **High-Reward, High-Risk**: Strategic bets requiring careful planning
- **Low-Reward, Low-Risk**: Safe options with limited upside
- **Low-Reward, High-Risk**: Options to avoid or redesign

### 5. Strategic Decision Framework

#### Decision Criteria
- **Strategic Alignment**: Does this support long-term goals?
- **LEVER Compliance**: Does this follow PIB principles?
- **Resource Feasibility**: Can we execute this with available resources?
- **Risk Tolerance**: Does the risk-reward profile match our capacity?
- **Learning Value**: Will this increase team capabilities and knowledge?

## Output Format

### Executive Summary
- Problem characterization and strategic context
- Agent perspective and expertise applied
- Key insights and strategic recommendations
- Confidence level and areas of uncertainty

### Deep Analysis
- Multi-dimensional problem exploration
- LEVER framework strategic validation
- Alternative solution evaluation
- Risk-reward assessment matrix

### Strategic Recommendations
- Prioritized strategic options with rationale
- Implementation approaches and sequencing
- Resource requirements and timeline estimates
- Success metrics and validation methods

### Implementation Roadmap
- Immediate actions and quick wins
- Medium-term strategic initiatives
- Long-term vision and evolution path
- Contingency plans and risk mitigation

### Workflow Integration
- Update workflow state with strategic insights
- Document strategic decisions in knowledge base
- Prepare context for implementation planning
- Schedule strategic review and validation sessions

## Integration with PIB System

### Workflow Hooks
- Triggers `read-workflow.sh` for LEVER principles integration
- Updates workflow state through `workflow-transition.sh`
- Logs strategic session through `notification-hook.sh`

### Knowledge Management
- Saves strategic analysis to `.ai/strategy/thinking-[timestamp].md`
- Updates strategic decision log and rationale
- Creates follow-up task tracking
- Updates project strategic context

### Follow-up Commands
Strategic thinking prepares context for:
- `*planner` - Detailed implementation planning
- `*analyze` - Technical feasibility analysis
- `*codereview` - Implementation approach validation
- `*consensus` - Multi-stakeholder strategic alignment

## Example Usage

```bash
# Strategic technology decision
*thinkdeep "Should we adopt microservices architecture?" architecture

# Product strategy analysis
*thinkdeep "How to prioritize feature development for next quarter" product-strategy

# Complex integration problem
*thinkdeep "Integrating legacy CRM with new customer portal" integration
```

## Quality Gates

All strategic thinking must meet PIB standards:
- **LEVER Compliance**: Demonstrates leverage, extension, verification, elimination, reduction
- **Agent Alignment**: Respects current agent expertise and strategic perspective
- **Strategic Rigor**: Follows systematic thinking methodology and evaluation criteria
- **Evidence-Based**: Grounded in concrete evidence and validated assumptions
- **Actionable Output**: Provides clear strategic direction and implementation guidance
