{"hooks": {"PostToolUse": [{"matcher": "", "hooks": [{"type": "command", "command": "bash -c 'DIR=$(pwd); while [ \"$DIR\" != \"/\" ] && [ ! -d \"$DIR/.claude\" ]; do DIR=$(dirname \"$DIR\"); done; [ -d \"$DIR/.claude\" ] && cat | \"$DIR/.claude/hooks/universal-hook-runner.sh\" agent-orchestration-hook.sh || exit 2'"}, {"type": "command", "command": "bash -c 'DIR=$(pwd); while [ \"$DIR\" != \"/\" ] && [ ! -d \"$DIR/.claude\" ]; do DIR=$(dirname \"$DIR\"); done; [ -d \"$DIR/.claude\" ] && cat | \"$DIR/.claude/hooks/universal-hook-runner.sh\" auto-review-agent.sh || exit 2'"}, {"type": "command", "command": "bash -c 'DIR=$(pwd); while [ \"$DIR\" != \"/\" ] && [ ! -d \"$DIR/.claude\" ]; do DIR=$(dirname \"$DIR\"); done; [ -d \"$DIR/.claude\" ] && cat | \"$DIR/.claude/hooks/universal-hook-runner.sh\" change-implementer-agent.sh || exit 2'"}, {"type": "command", "command": "bash -c 'DIR=$(pwd); while [ \"$DIR\" != \"/\" ] && [ ! -d \"$DIR/.claude\" ]; do DIR=$(dirname \"$DIR\"); done; [ -d \"$DIR/.claude\" ] && cat | \"$DIR/.claude/hooks/universal-hook-runner.sh\" notification-hook.sh || exit 2'"}, {"type": "command", "command": "bash -c 'DIR=$(pwd); while [ \"$DIR\" != \"/\" ] && [ ! -d \"$DIR/.claude\" ]; do DIR=$(dirname \"$DIR\"); done; [ -d \"$DIR/.claude\" ] && cat | \"$DIR/.claude/hooks/universal-hook-runner.sh\" confirmation-notifier.sh || exit 2'"}]}], "Stop": [{"matcher": "", "hooks": [{"type": "command", "command": "bash -c 'DIR=$(pwd); while [ \"$DIR\" != \"/\" ] && [ ! -d \"$DIR/.claude\" ]; do DIR=$(dirname \"$DIR\"); done; [ -d \"$DIR/.claude\" ] && cat | \"$DIR/.claude/hooks/universal-hook-runner.sh\" notification-hook.sh || exit 2'"}]}], "SubagentStop": [{"matcher": "", "hooks": [{"type": "command", "command": "bash -c 'DIR=$(pwd); while [ \"$DIR\" != \"/\" ] && [ ! -d \"$DIR/.claude\" ]; do DIR=$(dirname \"$DIR\"); done; [ -d \"$DIR/.claude\" ] && cat | \"$DIR/.claude/hooks/universal-hook-runner.sh\" notification-hook.sh || exit 2'"}]}]}}