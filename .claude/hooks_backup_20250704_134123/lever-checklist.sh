#!/bin/bash

# LEVER Framework Pre-Implementation Checklist
# Claude Code compatible - automatic analysis (no user interaction)

# Read JSON input from stdin (required for Claude Code hooks)
JSON_INPUT=$(cat)

# Extract tool name and file path
TOOL_NAME=$(echo "$JSON_INPUT" | jq -r '.toolName // empty' 2>/dev/null)
FILE_PATH=$(echo "$JSON_INPUT" | jq -r '.params.file_path // empty' 2>/dev/null)

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CLAUDE_DIR="$(dirname "$SCRIPT_DIR")"

WORKFLOW_LOG="$CLAUDE_DIR/workflow.log"
LEVER_REPORT="$CLAUDE_DIR/lever-analysis.md"

# Function to log
log_workflow() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] LEVER: $1" >> "$WORKFLOW_LOG"
}

# Detect file type if file path provided
if [ -n "$FILE_PATH" ]; then
    FILE_EXT="${FILE_PATH##*.}"
    FILE_NAME="$(basename "$FILE_PATH")"
    FILE_TYPE="general"
    
    # Detect file type based on extension
    case "$FILE_EXT" in
        js|jsx|ts|tsx) FILE_TYPE="javascript" ;;
        py) FILE_TYPE="python" ;;
        java) FILE_TYPE="java" ;;
        go) FILE_TYPE="go" ;;
        rb) FILE_TYPE="ruby" ;;
        php) FILE_TYPE="php" ;;
        sql) FILE_TYPE="database" ;;
        css|scss|sass|less) FILE_TYPE="stylesheet" ;;
        html|vue|svelte) FILE_TYPE="template" ;;
        json|yaml|yml|toml) FILE_TYPE="config" ;;
        md|txt|rst) FILE_TYPE="documentation" ;;
    esac
else
    FILE_TYPE="unknown"
    FILE_NAME="unknown"
fi

# Generate automatic analysis report
echo "# LEVER Framework Analysis Report" > "$LEVER_REPORT"
echo "Generated: $(date)" >> "$LEVER_REPORT"
echo "Tool: $TOOL_NAME" >> "$LEVER_REPORT"
echo "File: $FILE_NAME" >> "$LEVER_REPORT"
echo "" >> "$LEVER_REPORT"

# Display analysis in console
echo ""
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "              📊 LEVER FRAMEWORK AUTOMATIC ANALYSIS"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""

# Pattern Recognition
echo "🔍 PATTERN RECOGNITION CHECKLIST:"
echo "## Pattern Recognition Checklist" >> "$LEVER_REPORT"
echo "" >> "$LEVER_REPORT"

echo "  ☐ Search for similar functionality in existing code"
echo "  ☐ Check for related database queries/mutations"
echo "  ☐ Look for similar UI components or layouts"
echo "  ☐ Find hooks that manage similar state"
echo "  ☐ Review existing API endpoints for similar operations"
echo ""

echo "- Search for similar functionality in existing code" >> "$LEVER_REPORT"
echo "- Check for related database queries/mutations" >> "$LEVER_REPORT"
echo "- Look for similar UI components or layouts" >> "$LEVER_REPORT"
echo "- Find hooks that manage similar state" >> "$LEVER_REPORT"
echo "- Review existing API endpoints for similar operations" >> "$LEVER_REPORT"
echo "" >> "$LEVER_REPORT"

# Code Reuse Opportunities
echo "♻️  CODE REUSE ANALYSIS:"
echo "## Code Reuse Opportunities" >> "$LEVER_REPORT"
echo "" >> "$LEVER_REPORT"

case "$FILE_TYPE" in
    javascript)
        echo "  ☐ Can existing React components be extended with props?"
        echo "  ☐ Can existing hooks be enhanced with new state?"
        echo "  ☐ Can existing API calls be modified?"
        echo "  ☐ Can existing utilities be reused?"
        
        echo "- Can existing React components be extended with props?" >> "$LEVER_REPORT"
        echo "- Can existing hooks be enhanced with new state?" >> "$LEVER_REPORT"
        echo "- Can existing API calls be modified?" >> "$LEVER_REPORT"
        echo "- Can existing utilities be reused?" >> "$LEVER_REPORT"
        ;;
    python)
        echo "  ☐ Can existing classes be extended?"
        echo "  ☐ Can existing functions be enhanced with parameters?"
        echo "  ☐ Can existing modules be imported and reused?"
        echo "  ☐ Can existing decorators be applied?"
        
        echo "- Can existing classes be extended?" >> "$LEVER_REPORT"
        echo "- Can existing functions be enhanced with parameters?" >> "$LEVER_REPORT"
        echo "- Can existing modules be imported and reused?" >> "$LEVER_REPORT"
        echo "- Can existing decorators be applied?" >> "$LEVER_REPORT"
        ;;
    database)
        echo "  ☐ Can existing tables be extended with new columns?"
        echo "  ☐ Can existing views be modified?"
        echo "  ☐ Can existing stored procedures be enhanced?"
        echo "  ☐ Can existing indexes be reused?"
        
        echo "- Can existing tables be extended with new columns?" >> "$LEVER_REPORT"
        echo "- Can existing views be modified?" >> "$LEVER_REPORT"
        echo "- Can existing stored procedures be enhanced?" >> "$LEVER_REPORT"
        echo "- Can existing indexes be reused?" >> "$LEVER_REPORT"
        ;;
    *)
        echo "  ☐ Can existing code structures be extended?"
        echo "  ☐ Can existing functions be enhanced?"
        echo "  ☐ Can existing patterns be reused?"
        echo "  ☐ Can existing configurations be modified?"
        
        echo "- Can existing code structures be extended?" >> "$LEVER_REPORT"
        echo "- Can existing functions be enhanced?" >> "$LEVER_REPORT"
        echo "- Can existing patterns be reused?" >> "$LEVER_REPORT"
        echo "- Can existing configurations be modified?" >> "$LEVER_REPORT"
        ;;
esac

echo "" >> "$LEVER_REPORT"

# Complexity Guidelines
echo ""
echo "📏 COMPLEXITY GUIDELINES:"
echo "## Complexity Guidelines" >> "$LEVER_REPORT"
echo "" >> "$LEVER_REPORT"

echo "  • Prefer modifying 1 existing file over creating 1 new file"
echo "  • Prefer adding 10 lines to existing code over 50 new lines"
echo "  • Prefer extending existing APIs over creating new endpoints"
echo "  • Prefer reusing existing database structures"
echo ""

echo "- Prefer modifying 1 existing file over creating 1 new file" >> "$LEVER_REPORT"
echo "- Prefer adding 10 lines to existing code over 50 new lines" >> "$LEVER_REPORT"
echo "- Prefer extending existing APIs over creating new endpoints" >> "$LEVER_REPORT"
echo "- Prefer reusing existing database structures" >> "$LEVER_REPORT"
echo "" >> "$LEVER_REPORT"

# Automatic recommendation based on file type
echo "🎯 RECOMMENDATION:"
echo "## Automatic Recommendation" >> "$LEVER_REPORT"
echo "" >> "$LEVER_REPORT"

if [ "$TOOL_NAME" == "Write" ] && [ -n "$FILE_PATH" ]; then
    echo "  ⚠️  Creating new file: $FILE_NAME"
    echo "  📌 STOP! First search for similar files to extend instead!"
    echo "⚠️  Creating new file detected. Search for similar files to extend first!" >> "$LEVER_REPORT"
else
    echo "  ✅ Modifying existing code - following LEVER principles"
    echo "✅ Modifying existing code - good LEVER practice" >> "$LEVER_REPORT"
fi

echo ""
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# Log the analysis
log_workflow "Automatic pre-implementation analysis completed for $TOOL_NAME on $FILE_NAME"

# Send subtle notification
osascript -e 'display notification "LEVER analysis complete" with title "BMAD Framework"' 2>/dev/null || true


# Pass through the JSON for the next hook/tool
echo "$JSON_INPUT"
exit 0