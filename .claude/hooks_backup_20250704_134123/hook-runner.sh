#!/bin/bash

# Universal hook runner for Claude Code compatibility
# Runs hooks synchronously and passes stdin/stdout properly

# Function to find .claude directory
find_claude_dir() {
    local dir="$PWD"
    while [ "$dir" != "/" ]; do
        if [ -d "$dir/.claude" ]; then
            echo "$dir/.claude"
            return 0
        fi
        dir=$(dirname "$dir")
    done
    
    # If not found going up, check if we're already in .claude
    if [[ "$PWD" == *"/.claude"* ]]; then
        # Extract the .claude directory path
        echo "${PWD%/.claude*}/.claude"
        return 0
    fi
    
    return 1
}

# Find the .claude directory
CLAUDE_DIR=$(find_claude_dir)
if [ -z "$CLAUDE_DIR" ]; then
    echo "Error: Could not find .claude directory" >&2
    exit 2  # Blocking error
fi

# The hook to run is the first argument
HOOK_NAME="$1"
shift  # Remove hook name, pass remaining args to the hook

# Run the hook synchronously and pass stdin through
if [ -f "$CLAUDE_DIR/hooks/$HOOK_NAME" ]; then
    # CRITICAL: Use cat to pass stdin to the hook
    # This ensures Claude Code's JSON input reaches the hook
    cat | "$CLAUDE_DIR/hooks/$HOOK_NAME" "$@"
    exit_code=$?
    exit $exit_code
else
    echo "Hook not found: $HOOK_NAME" >&2
    exit 2  # Blocking error
fi