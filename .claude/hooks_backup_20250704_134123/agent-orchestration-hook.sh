#!/bin/bash

# Agent Orchestration Hook for BMAD Method
# Automatically triggers code review workflow after code changes
# Implements: code -> review -> change-implementer pipeline

# Set error handling
set -o pipefail
trap '' PIPE

# === DEPENDENCY VALIDATION ===
if ! command -v jq >/dev/null 2>&1; then
    echo "ERROR: jq is required but not installed. Please install jq." >&2
    exit 1
fi

# === READ JSON INPUT ===
if ! JSON_INPUT=$(cat 2>/dev/null); then
    exit 0
fi

# === EXTRACT EVENT DATA ===
EVENT_TYPE=$(echo "$JSON_INPUT" | jq -r '.event // empty' 2>/dev/null)
TOOL_NAME=$(echo "$JSON_INPUT" | jq -r '.toolName // empty' 2>/dev/null)
TOOL_RESULT=$(echo "$JSON_INPUT" | jq -r '.result // empty' 2>/dev/null)

# Get current project context
CURRENT_DIR=$(pwd)
PROJECT_NAME=$(basename "$CURRENT_DIR")

# Find .claude directory
find_claude_dir() {
    local dir="$CURRENT_DIR"
    while [ "$dir" != "/" ]; do
        if [ -d "$dir/.claude" ]; then
            echo "$dir/.claude"
            return 0
        fi
        dir=$(dirname "$dir")
    done
    return 1
}

CLAUDE_DIR=$(find_claude_dir)
if [ -z "$CLAUDE_DIR" ]; then
    echo "$JSON_INPUT"
    exit 0
fi

# === WORKFLOW STATE MANAGEMENT ===
WORKFLOW_STATE_FILE="$CLAUDE_DIR/current-workflow-state.json"
WORKFLOW_CONFIG_FILE="$CLAUDE_DIR/workflow-config.json"

# Load current workflow state
load_workflow_state() {
    if [ -f "$WORKFLOW_STATE_FILE" ]; then
        CURRENT_WORKFLOW=$(jq -r '.workflow // "development"' "$WORKFLOW_STATE_FILE" 2>/dev/null)
        CURRENT_STAGE=$(jq -r '.currentStage // "development"' "$WORKFLOW_STATE_FILE" 2>/dev/null)
        CURRENT_AGENT=$(jq -r '.currentAgent // "developer"' "$WORKFLOW_STATE_FILE" 2>/dev/null)
    else
        CURRENT_WORKFLOW="development"
        CURRENT_STAGE="development"
        CURRENT_AGENT="developer"
    fi
}

# Update workflow state
update_workflow_state() {
    local new_stage="$1"
    local new_agent="$2"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    cat > "$WORKFLOW_STATE_FILE" <<EOF
{
  "workflow": "$CURRENT_WORKFLOW",
  "currentStage": "$new_stage",
  "currentAgent": "$new_agent",
  "lastUpdate": "$timestamp",
  "triggeredBy": "agent-orchestration-hook",
  "context": {
    "lastTool": "$TOOL_NAME",
    "eventType": "$EVENT_TYPE"
  }
}
EOF
}

# === CODE CHANGE DETECTION ===
detect_code_changes() {
    case "$TOOL_NAME" in
        "Write"|"Edit"|"MultiEdit")
            return 0  # Code change detected
            ;;
        *)
            return 1  # No code change
            ;;
    esac
}

# === AGENT INVOCATION ===
invoke_code_review_agent() {
    local review_trigger_file="$CLAUDE_DIR/review-trigger.json"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    # Create review trigger file with context
    cat > "$review_trigger_file" <<EOF
{
  "trigger": "PostToolUse",
  "toolName": "$TOOL_NAME",
  "timestamp": "$timestamp",
  "previousAgent": "$CURRENT_AGENT",
  "previousStage": "$CURRENT_STAGE",
  "status": "pending_review",
  "context": {
    "projectName": "$PROJECT_NAME",
    "workingDirectory": "$CURRENT_DIR"
  }
}
EOF
    
    # Update workflow state to review stage
    update_workflow_state "review" "code-reviewer"
    
    # Log the transition
    echo "$(date): Agent orchestration triggered - transitioning to code review stage" >> "$CLAUDE_DIR/orchestration.log"
    
    return 0
}

invoke_change_implementer_agent() {
    local change_trigger_file="$CLAUDE_DIR/change-trigger.json"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    # Create change implementation trigger
    cat > "$change_trigger_file" <<EOF
{
  "trigger": "ReviewComplete",
  "timestamp": "$timestamp",
  "previousAgent": "code-reviewer",
  "previousStage": "review",
  "status": "pending_implementation",
  "context": {
    "projectName": "$PROJECT_NAME",
    "workingDirectory": "$CURRENT_DIR"
  }
}
EOF
    
    # Update workflow state to implementation stage
    update_workflow_state "implementation" "change-implementer"
    
    # Log the transition
    echo "$(date): Review complete - transitioning to change implementation stage" >> "$CLAUDE_DIR/orchestration.log"
    
    return 0
}

# === WORKFLOW ORCHESTRATION LOGIC ===
orchestrate_workflow() {
    load_workflow_state
    
    case "$EVENT_TYPE" in
        "PostToolUse")
            if detect_code_changes; then
                case "$CURRENT_STAGE" in
                    "development")
                        # Code change during development -> trigger review
                        invoke_code_review_agent
                        echo "$(date): Code change detected in development stage - triggering code review" >> "$CLAUDE_DIR/orchestration.log"
                        ;;
                    "review")
                        # Code change during review -> may need re-review
                        echo "$(date): Code change detected during review stage - may need re-review" >> "$CLAUDE_DIR/orchestration.log"
                        ;;
                    "implementation")
                        # Code change during implementation -> back to review
                        invoke_code_review_agent
                        echo "$(date): Code change detected in implementation stage - triggering re-review" >> "$CLAUDE_DIR/orchestration.log"
                        ;;
                esac
            fi
            ;;
        "ReviewComplete")
            # Review completed -> trigger change implementer
            if [ "$CURRENT_STAGE" = "review" ]; then
                invoke_change_implementer_agent
            fi
            ;;
        "ImplementationComplete")
            # Implementation completed -> back to development
            update_workflow_state "development" "developer"
            echo "$(date): Implementation complete - returning to development stage" >> "$CLAUDE_DIR/orchestration.log"
            ;;
    esac
}

# === INTELLIGENT WORKFLOW DETECTION ===
# Check if we should trigger automatic review based on context
should_trigger_automatic_review() {
    # Don't trigger during certain operations
    case "$TOOL_NAME" in
        "TodoWrite"|"TodoRead")
            return 1  # Skip todo operations
            ;;
        "Bash")
            # Skip bash operations unless they're code generation/build
            if echo "$TOOL_RESULT" | grep -q -E "(build|compile|generate|create.*\.js|create.*\.py|create.*\.ts)"; then
                return 0
            fi
            return 1
            ;;
        "Write"|"Edit"|"MultiEdit")
            # Always trigger for code changes
            return 0
            ;;
    esac
    return 1
}

# === MAIN ORCHESTRATION EXECUTION ===
if [ "$EVENT_TYPE" = "PostToolUse" ] && should_trigger_automatic_review; then
    orchestrate_workflow
fi

# === AGENT CONTEXT INJECTION ===
# Inject current agent context into the environment for next command
if [ -f "$WORKFLOW_STATE_FILE" ]; then
    export BMAD_CURRENT_AGENT="$CURRENT_AGENT"
    export BMAD_CURRENT_STAGE="$CURRENT_STAGE"
    export BMAD_WORKFLOW="$CURRENT_WORKFLOW"
fi

# CRITICAL: Pass through the JSON for the next hook/tool
echo "$JSON_INPUT"

# Always exit 0 - orchestration is informational/workflow management
exit 0