#!/bin/bash

# Manual Linting Command
# Run linting on-demand for the entire project or specific files

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CLAUDE_DIR="$(dirname "$SCRIPT_DIR")"

echo "=========================================="
echo "         BMAD Manual Linting"
echo "=========================================="
echo ""

# Check if specific files were provided
if [ $# -eq 0 ]; then
    echo "Running linting on entire project..."
    "$SCRIPT_DIR/detect-and-fix-lint.sh"
else
    echo "Running linting on specified files..."
    for file in "$@"; do
        echo "Linting: $file"
        "$SCRIPT_DIR/detect-and-fix-lint.sh" "$file"
    done
fi

# Display the report
if [ -f "$CLAUDE_DIR/last-lint-report.md" ]; then
    echo ""
    echo "=========================================="
    echo "         Linting Report"
    echo "=========================================="
    cat "$CLAUDE_DIR/last-lint-report.md"
fi

echo ""
echo "Linting complete!"

exit 0