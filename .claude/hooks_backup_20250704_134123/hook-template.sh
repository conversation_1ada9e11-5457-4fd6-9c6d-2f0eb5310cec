#!/bin/bash

# Claude Code Hook Template
# This template shows how to create a Claude Code compatible hook
# Hooks must:
# 1. Read JSON from stdin
# 2. Return exit code 0 (success) or 2 (blocking error)
# 3. Output messages to stdout/stderr for <PERSON> to display

# Read JSON input from stdin
JSON_INPUT=$(cat)

# Extract tool name (if needed)
TOOL_NAME=$(echo "$JSON_INPUT" | jq -r '.toolName // empty' 2>/dev/null)

# Extract file path for Write/Edit operations
FILE_PATH=$(echo "$JSON_INPUT" | jq -r '.params.file_path // empty' 2>/dev/null)

# Extract other parameters as needed
# PARAM=$(echo "$JSON_INPUT" | jq -r '.params.param_name // empty' 2>/dev/null)

# Log for debugging (optional - goes to stderr)
# echo "Hook received tool: $TOOL_NAME" >&2

# ======================
# ADD YOUR HOOK LOGIC HERE
# ======================

# Example: Check if we're editing a specific type of file
# if [[ "$FILE_PATH" == *.py ]] && [[ "$TOOL_NAME" == "Write" || "$TOOL_NAME" == "Edit" ]]; then
#     echo "Python file modified: $FILE_PATH"
#     # Do something...
# fi

# Exit codes:
# 0 = Success (non-blocking)
# 2 = Blocking error (prevents tool execution)
# Any other = Non-blocking error

exit 0