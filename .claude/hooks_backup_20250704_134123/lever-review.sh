#!/bin/bash

# LEVER Framework Review Verification
# Claude Code compatible - automatic review scoring (no user interaction)

# Read JSON input from stdin (required for Claude Code hooks)
JSON_INPUT=$(cat)

# Extract tool name
TOOL_NAME=$(echo "$JSON_INPUT" | jq -r '.toolName // empty' 2>/dev/null)

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CLAUDE_DIR="$(dirname "$SCRIPT_DIR")"

LEVER_REPORT="$CLAUDE_DIR/lever-analysis.md"
REVIEW_REPORT="$CLAUDE_DIR/lever-review-report.md"
WORKFLOW_LOG="$CLAUDE_DIR/workflow.log"
WORKFLOW_STATE="$CLAUDE_DIR/current-workflow-state.json"

# Function to log
log_workflow() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] LEVER Review: $1" >> "$WORKFLOW_LOG"
}

# Create review report
echo "# LEVER Framework Review Report" > "$REVIEW_REPORT"
echo "Generated: $(date)" >> "$REVIEW_REPORT"
echo "" >> "$REVIEW_REPORT"

# Display review header
echo ""
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "              🔍 LEVER FRAMEWORK REVIEW VERIFICATION"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""

# Check if pre-implementation analysis was done
echo "## Pre-Implementation Analysis Check" >> "$REVIEW_REPORT"
echo "" >> "$REVIEW_REPORT"

if [ -f "$LEVER_REPORT" ]; then
    echo "✅ Pre-implementation LEVER analysis found"
    echo "✅ Pre-implementation LEVER analysis was completed" >> "$REVIEW_REPORT"
    
    # Extract analysis time
    ANALYSIS_TIME=$(grep "Generated:" "$LEVER_REPORT" | head -1)
    echo "   $ANALYSIS_TIME" >> "$REVIEW_REPORT"
else
    echo "⚠️  WARNING: No pre-implementation LEVER analysis found!"
    echo "⚠️  WARNING: No pre-implementation LEVER analysis found!" >> "$REVIEW_REPORT"
    echo "   This suggests LEVER principles may not have been followed" >> "$REVIEW_REPORT"
fi

echo "" >> "$REVIEW_REPORT"

# Update workflow state
if [ -f "$WORKFLOW_STATE" ]; then
    CURRENT_STAGE=$(jq -r '.currentStage' "$WORKFLOW_STATE")
    if [ "$CURRENT_STAGE" != "review" ]; then
        # Update to review stage
        jq '.currentStage = "review" | .lastTransition = "'"$(date -u +%Y-%m-%dT%H:%M:%SZ)"'"' "$WORKFLOW_STATE" > "$WORKFLOW_STATE.tmp" && mv "$WORKFLOW_STATE.tmp" "$WORKFLOW_STATE"
    fi
fi

# LEVER Principle Checklist
echo ""
echo "📋 LEVER COMPLIANCE CHECKLIST:"
echo "## LEVER Compliance Checklist" >> "$REVIEW_REPORT"
echo "" >> "$REVIEW_REPORT"

echo ""
echo "When reviewing code, verify:"
echo ""

echo "### L - Leverage Existing Patterns" >> "$REVIEW_REPORT"
echo "✓ Check: Did the code reuse existing patterns?" >> "$REVIEW_REPORT"
echo "✓ Check: Were similar implementations referenced?" >> "$REVIEW_REPORT"
echo "" >> "$REVIEW_REPORT"

echo "  L - LEVERAGE"
echo "    ✓ Did the code reuse existing patterns?"
echo "    ✓ Were similar implementations referenced?"
echo ""

echo "### E - Extend Before Creating" >> "$REVIEW_REPORT"
echo "✓ Check: Were existing components/tables/queries extended?" >> "$REVIEW_REPORT"
echo "✓ Check: Was new code minimized in favor of extensions?" >> "$REVIEW_REPORT"
echo "" >> "$REVIEW_REPORT"

echo "  E - EXTEND"
echo "    ✓ Were existing components/tables/queries extended?"
echo "    ✓ Was new code minimized in favor of extensions?"
echo ""

echo "### V - Verify Through Reactivity" >> "$REVIEW_REPORT"
echo "✓ Check: Are reactive patterns used effectively?" >> "$REVIEW_REPORT"
echo "✓ Check: Is the framework's reactivity leveraged?" >> "$REVIEW_REPORT"
echo "" >> "$REVIEW_REPORT"

echo "  V - VERIFY"
echo "    ✓ Are reactive patterns used effectively?"
echo "    ✓ Is the framework's reactivity leveraged?"
echo ""

echo "### E - Eliminate Duplication" >> "$REVIEW_REPORT"
echo "✓ Check: Is there any code duplication?" >> "$REVIEW_REPORT"
echo "✓ Check: Are common patterns abstracted?" >> "$REVIEW_REPORT"
echo "" >> "$REVIEW_REPORT"

echo "  E - ELIMINATE"
echo "    ✓ Is there any code duplication?"
echo "    ✓ Are common patterns abstracted?"
echo ""

echo "### R - Reduce Complexity" >> "$REVIEW_REPORT"
echo "✓ Check: Is the solution as simple as possible?" >> "$REVIEW_REPORT"
echo "✓ Check: Can the code be further simplified?" >> "$REVIEW_REPORT"
echo "" >> "$REVIEW_REPORT"

echo "  R - REDUCE"
echo "    ✓ Is the solution as simple as possible?"
echo "    ✓ Can the code be further simplified?"
echo ""

# Automated recommendations
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""
echo "💡 REVIEW RECOMMENDATIONS:"
echo "## Review Recommendations" >> "$REVIEW_REPORT"
echo "" >> "$REVIEW_REPORT"

echo "  1. Verify code extends existing functionality"
echo "  2. Check for unnecessary new files or tables"
echo "  3. Ensure reactive patterns are used"
echo "  4. Look for code duplication"
echo "  5. Assess overall complexity"
echo ""

echo "1. Verify code extends existing functionality" >> "$REVIEW_REPORT"
echo "2. Check for unnecessary new files or tables" >> "$REVIEW_REPORT"
echo "3. Ensure reactive patterns are used" >> "$REVIEW_REPORT"
echo "4. Look for code duplication" >> "$REVIEW_REPORT"
echo "5. Assess overall complexity" >> "$REVIEW_REPORT"
echo "" >> "$REVIEW_REPORT"

# Role reminder
echo "🎭 ROLE REMINDER:"
echo "## Role Reminder" >> "$REVIEW_REPORT"
echo "" >> "$REVIEW_REPORT"

echo "  You are now in REVIEWER role. Please:"
echo "  • Critically evaluate the implementation"
echo "  • Check LEVER compliance"
echo "  • Suggest improvements for code reuse"
echo "  • Identify any missed extension opportunities"
echo ""

echo "You are now in REVIEWER role. Please:" >> "$REVIEW_REPORT"
echo "- Critically evaluate the implementation" >> "$REVIEW_REPORT"
echo "- Check LEVER compliance" >> "$REVIEW_REPORT"
echo "- Suggest improvements for code reuse" >> "$REVIEW_REPORT"
echo "- Identify any missed extension opportunities" >> "$REVIEW_REPORT"
echo "" >> "$REVIEW_REPORT"

# If review fails, provide guidance for rewrite
echo "📝 IF CHANGES NEEDED:"
echo "## If Changes Needed" >> "$REVIEW_REPORT"
echo "" >> "$REVIEW_REPORT"

echo "  • Document specific LEVER violations"
echo "  • Identify existing code that could be extended"
echo "  • Suggest simpler alternatives"
echo "  • Return to development stage for fixes"
echo ""

echo "- Document specific LEVER violations" >> "$REVIEW_REPORT"
echo "- Identify existing code that could be extended" >> "$REVIEW_REPORT"
echo "- Suggest simpler alternatives" >> "$REVIEW_REPORT"
echo "- Return to development stage for fixes" >> "$REVIEW_REPORT"

echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# Log the review
log_workflow "LEVER review verification displayed"

# Save review report location
echo ""
echo "Review report saved to: lever-review-report.md"

# Send notification for role change
osascript -e 'display notification "Switch to REVIEWER role - Check LEVER compliance" with title "BMAD Review Phase" sound name "Glass"' 2>/dev/null || true


# Pass through the JSON for the next hook/tool
echo "$JSON_INPUT"
exit 0