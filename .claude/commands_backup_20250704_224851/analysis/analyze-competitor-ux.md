---
description: Analyze competitor U<PERSON> patterns using Design Architect expertise for strategic insights
---

# BMAD Analyze Competitor UX Command

**Usage**: `*analyze-competitor-ux [competitor-list] [options]`

## Purpose

Analyzes competitor UX patterns using Design Architect expertise, leveraging BMAD principles to understand market UX standards while identifying opportunities to extend beyond current competitive offerings.

### Key Benefits
- Leverages existing market research and competitive intelligence
- Extends current UX understanding with detailed competitor pattern analysis
- Reduces UX design risk by understanding proven market patterns
- Integrates seamlessly with BMAD design and enhancement workflows

### When to Use
- Before creating UX/UI specifications for competitive context
- When planning product enhancements based on competitive gaps
- As part of design strategy development and validation
- When evaluating current UX against market standards

## Implementation

### Step-by-Step Algorithm

#### 1. LEVER Framework Pre-Validation
Before executing, validate these principles:
- **L** - Leverage: Existing competitive research and market analysis
- **E** - Extend: Current UX capabilities beyond competitive standards
- **V** - Verify: UX insights against user research and design principles
- **E** - Eliminate: UX patterns that create user friction or confusion
- **R** - Reduce: UX complexity while maximizing user value and differentiation

#### 2. Context Gathering
```bash
# Load project and competitive context
CURRENT_AGENT=$(cat .claude/current-workflow-state.json | jq -r '.currentAgent // "design-architect"')
PROJECT_CONTEXT=$(cat .ai/project-context.md 2>/dev/null || echo "No project context available")
CURRENT_UX=$(cat docs/uxui-spec.md 2>/dev/null || echo "No UX specifications found")
COMPETITIVE_RESEARCH=$(cat docs/competitive-analysis.md 2>/dev/null || echo "No competitive analysis found")
```

#### 3. Agent-Specific Processing
Adapt behavior based on active agent persona:

##### If Design Architect Active
- Focus on detailed UX pattern analysis and interaction design insights
- Component-level UX comparison and design system evaluation
- User flow optimization and accessibility pattern analysis

##### If Analyst Active  
- Emphasize market research methodology and competitive intelligence gathering
- User behavior analysis and UX trend identification
- Data-driven UX pattern validation and market positioning

#### 4. Core Processing
- Load Design Architect persona and competitor UX analysis task
- Research competitor UX patterns using available tools and analysis
- Create comprehensive UX competitive analysis using established framework
- Document UX patterns, interaction designs, and differentiation opportunities
- Generate actionable insights for UX strategy and enhancement

#### 5. Output Generation
- Structured competitor UX analysis saved to `docs/competitor-ux-analysis.md`
- UX pattern library with competitive benchmarking
- Interaction design insights and best practice documentation
- UX differentiation opportunities and enhancement recommendations

#### 6. Workflow Integration
- Updates workflow state to indicate competitive UX analysis completion
- Prepares context for UX/UI specification enhancement
- Integrates with design strategy and enhancement planning workflows

## Quality Features / Validation Checklist

### BMAD Compliance Validation
- [ ] **LEVER Principles**: Builds upon existing competitive research and UX knowledge
- [ ] **Agent Alignment**: Uses Design Architect UX expertise and analysis capabilities
- [ ] **Workflow Integration**: Properly follows design research and strategy workflow
- [ ] **Knowledge Capture**: Documents UX insights and competitive intelligence
- [ ] **Context Awareness**: Builds upon existing project UX and market context

### Technical Quality Gates
- [ ] **Input Validation**: Handles missing competitive data or UX specifications gracefully
- [ ] **Error Handling**: Provides guidance when competitor information is incomplete
- [ ] **Performance**: Efficient UX analysis and pattern identification process
- [ ] **Consistency**: Maintains coherent UX evaluation criteria and methodology
- [ ] **Documentation**: Clear, actionable UX insights and recommendations

### Output Quality Assurance
- [ ] **Completeness**: Covers major UX patterns and interaction design elements
- [ ] **Accuracy**: UX analysis is based on current and relevant competitive examples
- [ ] **Relevance**: Insights align with project UX needs and target user requirements
- [ ] **Actionability**: Provides specific UX enhancement and differentiation strategies
- [ ] **Integration**: Seamlessly connects with existing UX specifications and design strategy

### Knowledge Management Standards
- [ ] **Persistence**: Saves UX analysis to `docs/competitor-ux-analysis.md`
- [ ] **Versioning**: Maintains UX insight history and competitive intelligence updates
- [ ] **Cross-Reference**: Links to competitive analysis and UX specifications
- [ ] **Searchability**: Structured for easy reference during design decision-making
- [ ] **Agent Context**: Updates Design Architect and product team UX knowledge

## Integration

### Workflow Hooks
- **Triggers**: `read-workflow.sh` for LEVER principles reminder
- **Updates**: `workflow-transition.sh` for competitive UX analysis completion
- **Notifications**: `notification-hook.sh` for UX analysis completion alerts
- **Context**: Updates `.claude/current-workflow-state.json`

### Knowledge Management Integration
- **Saves**: Competitor UX analysis to `docs/competitor-ux-analysis.md`
- **Updates**: Project context with UX competitive intelligence in `.ai/project-context.md`
- **Creates**: UX research tracking in `.ai/ux-research/` directory
- **Links**: Cross-references with competitive analysis and design specifications

### Agent Context Updates
- **Design Architect**: Updates UX pattern knowledge and competitive benchmarking
- **All Agents**: Updates shared project competitive UX context
- **PM**: Provides UX-based enhancement opportunities and market positioning
- **Development**: Provides UX implementation standards and competitive benchmarks

### Follow-up Command Preparation
Results prepare context for:
- `*create-uxui-spec` - Enhanced UX specifications based on competitive insights
- `*create-enhancement-prd` - UX-focused enhancement requirements
- `*compare-competitor-features` - Feature-level competitive analysis
- `*update-knowledge` - Distribute UX competitive intelligence to all agents

## Related Commands

### Core Design Commands
- `*create-uxui-spec` - UX specifications enhanced by competitive analysis
- `*compare-competitor-features` - Feature-level competitive analysis
- `*create-frontend-architecture` - Frontend architecture with competitive context

### Workflow Commands
- `*create-enhancement-prd` - Enhancement planning based on UX competitive gaps
- `*plan-workflow` - Design workflow planning with competitive insights
- `*correct-course` - Strategic pivots based on UX competitive intelligence

### Agent Commands
- `/switch-agent design-architect` - Optimal agent for UX competitive analysis
- `/switch-agent analyst` - Research-focused competitive intelligence gathering

### Knowledge Commands
- `/update-knowledge` - Distribute UX competitive intelligence to all agents
- `/memory-extract` - Extract UX insights and design pattern intelligence

## Example Usage

### Basic Usage
```bash
# Analyze competitor UX patterns for current project
*analyze-competitor-ux
```

### Advanced Usage
```bash
# Analyze specific competitors with UX focus areas
*analyze-competitor-ux "competitor1,competitor2" --focus=mobile-ux,checkout-flow
```

### Agent-Specific Usage
```bash
# When Design Architect agent is active:
*analyze-competitor-ux --detailed-patterns --interaction-design

# When Analyst agent is active:
*analyze-competitor-ux --market-research --trend-analysis
```

### Workflow Integration Example
```bash
# Part of competitive UX enhancement workflow:
/analyst-brief  # Initial competitive landscape
*analyze-competitor-ux  # ← Detailed UX competitive analysis
/compare-competitor-features
/create-enhancement-prd
/create-uxui-spec  # Enhanced UX specifications
```

## Notes

### Agent Personas
This command adapts its behavior based on the active agent:
- **Design Architect**: Detailed UX pattern analysis with interaction design focus
- **Analyst**: Research methodology and competitive intelligence gathering
- **PM**: Business impact analysis of UX competitive positioning
- **QA**: UX testing standards and competitive quality benchmarking
- **Developer**: Implementation feasibility of competitive UX patterns

### LEVER Framework Emphasis
Every execution must demonstrate:
- **Leverage**: Existing competitive research, market analysis, and UX pattern libraries
- **Extend**: Current UX understanding with detailed competitive pattern analysis
- **Verify**: UX insights against user research, design principles, and market validation
- **Eliminate**: UX patterns that create user friction or competitive disadvantage
- **Reduce**: UX complexity while identifying opportunities for competitive differentiation

### Best Practices
- Focus on UX patterns that provide clear competitive advantages
- Document interaction design details and user flow optimizations
- Include accessibility and responsive design competitive benchmarking
- Validate UX insights against target user needs and business objectives