---
description: Comprehensive code review following BMAD quality standards and LEVER framework
---

# BMAD Code Review Command

**Usage**: `*codereview [files/directories] [review-focus]`

## Purpose

Perform comprehensive code review using BMAD quality standards, LEVER framework validation, and agent-specific review criteria to ensure code excellence and maintainability.

## LEVER Framework Pre-Review

Before reviewing code, validate these principles:
- **L** - Leverage: Does the code reuse existing patterns, utilities, and components?
- **E** - Extend: Does the code extend existing functionality rather than duplicating?
- **V** - Verify: Can the code's correctness be verified through existing test patterns?
- **E** - Eliminate: Does the code eliminate duplication and unnecessary complexity?
- **R** - Reduce: Is the code as simple as possible while maintaining functionality?

## Agent-Specific Review Focus

### Current Agent Context
Adapt review criteria based on active agent persona:

#### If Developer Agent Active
- Focus on implementation quality and coding standards
- Review variable naming, function structure, and code organization
- Assess error handling and edge case coverage
- Check for code smells and refactoring opportunities

#### If Architect Agent Active
- Focus on architectural compliance and design patterns
- Review component boundaries and separation of concerns
- Assess scalability and maintainability implications
- Check integration patterns and dependency management

#### If QA Agent Active
- Focus on testability and quality assurance aspects
- Review error conditions and boundary case handling
- Assess test coverage and validation completeness
- Check for potential runtime issues and edge cases

#### If Security Agent Active
- Focus on security vulnerabilities and best practices
- Review input validation and sanitization
- Assess authentication and authorization patterns
- Check for potential security risks and compliance issues

## Comprehensive Review Process

### 1. Initial Assessment
```bash
# Capture review context
echo "## Code Review Session: $(date)" >> .ai/review-log.md
echo "### Files: $FILES_DIRECTORIES" >> .ai/review-log.md
echo "### Focus: $REVIEW_FOCUS" >> .ai/review-log.md
echo "### Agent Context: $CURRENT_AGENT" >> .ai/review-log.md
```

### 2. LEVER Compliance Validation
- **Leverage Analysis**: Identify opportunities to use existing patterns
- **Extension Assessment**: Check if new code extends vs duplicates functionality
- **Verification Review**: Ensure code can be verified through existing test patterns
- **Elimination Check**: Remove unnecessary duplication and complexity
- **Reduction Validation**: Confirm code is as simple as possible while functional

### 3. Code Quality Assessment

#### Structure and Organization
- File and directory organization
- Function and class structure
- Module boundaries and dependencies
- Code cohesion and coupling

#### Readability and Maintainability
- Variable and function naming
- Comment quality and documentation
- Code clarity and expressiveness
- Consistent coding style

#### Performance and Efficiency
- Algorithm efficiency and complexity
- Resource usage and optimization
- Caching and performance patterns
- Scalability considerations

#### Error Handling and Robustness
- Exception handling patterns
- Input validation and sanitization
- Edge case coverage
- Graceful degradation

### 4. BMAD Standards Validation

#### Project Structure Compliance
- Follows established directory structure
- Uses correct file naming conventions
- Maintains proper module organization
- Integrates with existing architecture

#### Technology Stack Alignment
- Uses approved technologies and frameworks
- Follows established patterns and conventions
- Integrates properly with existing systems
- Maintains compatibility requirements

#### Documentation Standards
- Includes appropriate inline documentation
- Updates relevant README and guides
- Maintains API documentation
- Documents architectural decisions

## Review Categories and Criteria

### Code Quality (Weight: 25%)
- **Excellent**: Clean, readable, well-structured code following best practices
- **Good**: Minor style or organization issues, generally well-written
- **Fair**: Some code smells or structural issues requiring attention
- **Poor**: Significant quality issues requiring major refactoring

### LEVER Compliance (Weight: 25%)
- **Excellent**: Fully leverages existing patterns, extends appropriately, eliminates duplication
- **Good**: Good use of existing patterns with minor extension opportunities
- **Fair**: Some duplication or missed extension opportunities
- **Poor**: Significant duplication or failure to leverage existing code

### Architecture Alignment (Weight: 20%)
- **Excellent**: Perfect alignment with architectural patterns and decisions
- **Good**: Good alignment with minor architectural considerations
- **Fair**: Some architectural concerns requiring attention
- **Poor**: Significant architectural misalignment requiring redesign

### Testing and Validation (Weight: 15%)
- **Excellent**: Comprehensive test coverage with excellent edge case handling
- **Good**: Good test coverage with minor gaps
- **Fair**: Adequate testing with some missing coverage
- **Poor**: Insufficient testing requiring significant additions

### Documentation (Weight: 15%)
- **Excellent**: Comprehensive, clear documentation following standards
- **Good**: Good documentation with minor improvements needed
- **Fair**: Basic documentation requiring enhancement
- **Poor**: Insufficient documentation requiring major additions

## Output Format

### Review Summary
- Overall quality score and rating
- Agent perspective and focus areas
- LEVER compliance assessment
- Key strengths and concerns

### Detailed Findings
- Code quality assessment by category
- Specific issues identified with severity levels
- BMAD standards compliance evaluation
- Performance and security considerations

### Improvement Recommendations
- Priority-ranked improvement suggestions
- LEVER-compliant enhancement opportunities
- Specific code changes with examples
- Architecture and design improvements

### Implementation Plan
- Immediate fixes required before merge
- Medium-term improvements for next iteration
- Long-term architectural enhancements
- Testing and validation requirements

### Workflow Integration
- Update workflow state with review results
- Document findings in knowledge base
- Prepare context for implementation
- Schedule follow-up reviews

## Integration with BMAD System

### Workflow Hooks
- Triggers `read-workflow.sh` for LEVER principles reminder
- Updates workflow state through `workflow-transition.sh`
- Logs review session through `notification-hook.sh`

### Knowledge Management
- Saves review results to `.ai/reviews/review-[timestamp].md`
- Updates code quality metrics and trends
- Creates improvement task tracking
- Updates project quality guidelines

### Follow-up Commands
Review results prepare context for:
- `*refactor` - Implementation of improvement recommendations
- `*testgen` - Additional test coverage for identified gaps
- `*secaudit` - Security-focused review of flagged areas
- `*analyze` - Deeper analysis of complex issues

## Example Usage

```bash
# Review specific feature implementation
*codereview src/features/user-profile/ implementation

# Review entire API layer focusing on security
*codereview src/api/ security

# Review recent changes for quality assessment
*codereview $(git diff --name-only HEAD~1) quality
```

## Quality Gates

All code reviews must meet BMAD standards:
- **LEVER Compliance**: Code demonstrates leverage, extension, verification, elimination, reduction
- **Agent Alignment**: Review respects current agent expertise and focus areas
- **Quality Standards**: Meets established coding standards and best practices
- **Architecture Compliance**: Aligns with project architecture and design decisions
- **Knowledge Capture**: Documents findings and recommendations for future reference
