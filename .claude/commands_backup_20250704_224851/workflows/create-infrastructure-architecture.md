---
description: Create comprehensive infrastructure architecture using Architect expertise for scalable systems
---

# BMAD Create Infrastructure Architecture Command

**Usage**: `*create-infrastructure-architecture [scope] [options]`

## Purpose

Creates comprehensive infrastructure architecture using Architect expertise, leveraging BMAD principles to build upon existing system architecture while extending capabilities for scalable, resilient infrastructure design.

### Key Benefits
- Leverages existing system architecture and infrastructure patterns
- Extends current architectural documentation with detailed infrastructure specifications
- Reduces infrastructure risk through comprehensive planning and proven patterns
- Integrates seamlessly with BMAD system design and deployment workflows

### When to Use
- After system architecture is established and infrastructure needs are identified
- When planning complex infrastructure for microservices or distributed systems
- As part of platform engineering and enterprise infrastructure design
- When standardizing infrastructure architecture across environments

## Implementation

### Step-by-Step Algorithm

#### 1. LEVER Framework Pre-Validation
Before executing, validate these principles:
- **L** - Leverage: Existing system architecture, infrastructure patterns, and cloud services
- **E** - Extend: Current architectural capabilities with robust infrastructure design
- **V** - Verify: Infrastructure design against scalability, reliability, and security requirements
- **E** - Eliminate: Infrastructure complexity that doesn't provide operational value
- **R** - Reduce: Infrastructure management overhead while maximizing system resilience

#### 2. Context Gathering
```bash
# Load project and architectural context
CURRENT_AGENT=$(cat .claude/current-workflow-state.json | jq -r '.currentAgent // "architect"')
PROJECT_CONTEXT=$(cat .ai/project-context.md 2>/dev/null || echo "No project context available")
SYSTEM_ARCHITECTURE=$(cat docs/architecture.md 2>/dev/null || echo "No system architecture found")
PLATFORM_ARCH=$(cat docs/platform-architecture.md 2>/dev/null || echo "No platform architecture found")
CURRENT_PRD=$(cat docs/prd.md 2>/dev/null || echo "No PRD found")
```

#### 3. Agent-Specific Processing
Adapt behavior based on active agent persona:

##### If Architect Active
- Focus on comprehensive infrastructure architecture and system integration
- Detailed service architecture, data flow, and infrastructure component design
- Security architecture and compliance framework integration

##### If Platform Engineer Active  
- Emphasize platform-level infrastructure orchestration and automation
- Container orchestration, service mesh, and developer platform architecture
- Infrastructure as code and self-service platform capabilities

#### 4. Core Processing
- Load Architect persona and infrastructure architecture creation task
- Analyze existing system architecture and infrastructure requirements
- Create comprehensive infrastructure architecture using established template
- Document infrastructure components, networking, security, and operational requirements
- Generate infrastructure implementation roadmap and automation specifications

#### 5. Output Generation
- Structured infrastructure architecture saved to `docs/infrastructure-architecture.md`
- Infrastructure component specifications and integration documentation
- Network architecture and security framework specifications
- Operational requirements and monitoring architecture documentation

#### 6. Workflow Integration
- Updates workflow state to indicate infrastructure architecture completion
- Prepares context for deployment planning and platform implementation
- Integrates with system architecture and DevOps workflows

## Quality Features / Validation Checklist

### BMAD Compliance Validation
- [ ] **LEVER Principles**: Leverages existing architecture and proven infrastructure patterns
- [ ] **Agent Alignment**: Uses Architect or Platform Engineer infrastructure expertise
- [ ] **Workflow Integration**: Properly follows architectural design workflow sequence
- [ ] **Knowledge Capture**: Documents infrastructure decisions and design rationale
- [ ] **Context Awareness**: Builds upon existing system architecture and requirements

### Technical Quality Gates
- [ ] **Input Validation**: Handles missing system architecture or incomplete requirements
- [ ] **Error Handling**: Provides guidance when architectural prerequisites are missing
- [ ] **Performance**: Efficient infrastructure design and documentation process
- [ ] **Consistency**: Maintains coherent infrastructure design patterns and standards
- [ ] **Documentation**: Clear, implementable infrastructure specifications

### Output Quality Assurance
- [ ] **Completeness**: Covers all infrastructure components and operational requirements
- [ ] **Accuracy**: Infrastructure specifications are technically sound and feasible
- [ ] **Relevance**: Architecture aligns with system requirements and operational needs
- [ ] **Actionability**: Provides specific implementation guidance for infrastructure
- [ ] **Integration**: Seamlessly connects with system architecture and deployment plans

### Knowledge Management Standards
- [ ] **Persistence**: Saves infrastructure architecture to `docs/infrastructure-architecture.md`
- [ ] **Versioning**: Maintains infrastructure design history and architectural evolution
- [ ] **Cross-Reference**: Links to system architecture and deployment documentation
- [ ] **Searchability**: Structured for easy reference during implementation
- [ ] **Agent Context**: Updates Architect and infrastructure team knowledge

## Integration

### Workflow Hooks
- **Triggers**: `read-workflow.sh` for LEVER principles reminder
- **Updates**: `workflow-transition.sh` for infrastructure architecture completion
- **Notifications**: `notification-hook.sh` for architecture completion alerts
- **Context**: Updates `.claude/current-workflow-state.json`

### Knowledge Management Integration
- **Saves**: Infrastructure architecture to `docs/infrastructure-architecture.md`
- **Updates**: Project context with infrastructure specifications in `.ai/project-context.md`
- **Creates**: Infrastructure design tracking in `.ai/infrastructure/` directory
- **Links**: Cross-references with system architecture and operational documentation

### Agent Context Updates
- **Architect**: Updates infrastructure design knowledge and implementation patterns
- **All Agents**: Updates shared project infrastructure context
- **Platform Engineer**: Provides platform-level infrastructure specifications
- **DevOps**: Provides infrastructure implementation and operational requirements

### Follow-up Command Preparation
Results prepare context for:
- `*create-deployment-plan` - Deployment planning based on infrastructure architecture
- `*create-platform-infrastructure` - Platform-level infrastructure implementation
- `*validate-infrastructure` - Infrastructure design validation and feasibility
- `*update-knowledge` - Distribute infrastructure specifications to all agents

## Related Commands

### Core Architecture Commands
- `*architect-design` - System architecture prerequisites for infrastructure design
- `*create-platform-infrastructure` - Platform-level infrastructure implementation
- `*validate-infrastructure` - Infrastructure design validation

### Workflow Commands
- `*create-deployment-plan` - Deployment planning based on infrastructure
- `*review-infrastructure` - Infrastructure design review and optimization
- `*platform-change-management` - Platform-level infrastructure changes

### Agent Commands
- `/switch-agent architect` - Optimal agent for infrastructure architecture design
- `/switch-agent platform-engineer` - Platform-focused infrastructure design

### Knowledge Commands
- `/update-knowledge` - Distribute infrastructure specifications to all agents
- `/memory-extract` - Extract infrastructure design insights and decisions

## Example Usage

### Basic Usage
```bash
# Create infrastructure architecture for current project
*create-infrastructure-architecture
```

### Advanced Usage
```bash
# Create infrastructure architecture with specific scope and patterns
*create-infrastructure-architecture microservices --cloud-native --security-first
```

### Agent-Specific Usage
```bash
# When Architect agent is active:
*create-infrastructure-architecture --comprehensive --security-architecture

# When Platform Engineer agent is active:
*create-infrastructure-architecture --platform-focus --automation
```

### Workflow Integration Example
```bash
# Part of comprehensive architecture workflow:
/architect-design  # System architecture
*create-infrastructure-architecture  # ← Infrastructure architecture
/create-platform-infrastructure  # Platform implementation
/create-deployment-plan  # Deployment planning
/update-knowledge
```

## Notes

### Agent Personas
This command adapts its behavior based on the active agent:
- **Architect**: Comprehensive infrastructure architecture with system integration focus
- **Platform Engineer**: Platform-level infrastructure with automation and self-service focus
- **DevOps**: Operational infrastructure with deployment and monitoring focus
- **Security**: Security-first infrastructure with compliance and threat modeling
- **Data Scientist**: Data infrastructure with analytics and processing focus

### LEVER Framework Emphasis
Every execution must demonstrate:
- **Leverage**: Existing system architecture, proven infrastructure patterns, and cloud services
- **Extend**: Current architectural capabilities with robust, scalable infrastructure design
- **Verify**: Infrastructure design against operational requirements, security standards, and scalability needs
- **Eliminate**: Infrastructure complexity that doesn't provide operational or business value
- **Reduce**: Infrastructure management overhead while maximizing system resilience and automation

### Best Practices
- Run after system architecture is established for optimal context and integration
- Include security architecture and compliance requirements from the start
- Design for automation and infrastructure as code from the beginning
- Document operational procedures and monitoring requirements alongside architecture