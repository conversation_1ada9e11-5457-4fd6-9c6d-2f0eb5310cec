# Dev Command - Intelligent Development Workflow

Intelligent development command that automatically creates enriched work specifications and orchestrates agent workflows using MCP tools for enhanced development efficiency.

## Usage

Basic development request:
```
/dev "implement user authentication"
```

Complex development with specific requirements:
```
/dev "implement user authentication with OAuth support and session management"
```

Specify priority and agent focus:
```
/dev "implement user authentication" --priority=high --focus=security
```

Status check for active development workflows:
```
/dev --status
```

## Purpose

Transform development requests into intelligent, context-aware workflows that:
- Parse requirements using Zen MCP for multi-model analysis
- Research existing patterns with Context7 MCP 
- Create enriched work specifications with implementation guidance
- Automatically orchestrate agent coordination
- Maintain state across complex development cycles

## Key Features

### 1. Dynamic Context Engineering
- **Right Information**: Relevance-scored context extraction based on task type and user input
- **Right Format**: Agent-specific context packages with filtered, actionable information
- **Right Time**: Just-in-time context updates during workflow execution
- **Intelligent Analysis**: Uses Zen MCP for multi-model requirement analysis and validation
- **Pattern Research**: Leverages Context7 MCP to identify existing solutions and best practices
- **Work Specification**: Creates detailed specifications with enriched technical context

### 2. Automatic Agent Orchestration
- **Smart Agent Selection**: Determines optimal agent(s) based on task complexity and requirements
- **Workflow Coordination**: Manages transitions between development, review, and implementation phases
- **Context Preservation**: Maintains rich context throughout the development lifecycle

### 3. Enhanced Development Quality
- **LEVER Framework Compliance**: Ensures leverage of existing patterns and minimal code creation
- **MCP Tool Integration**: Uses appropriate tools for analysis, validation, and implementation
- **Quality Gates**: Integrates with existing review and validation processes

## Workflow Process

### Phase 1: Intelligent Analysis
```
User Request → Zen MCP Analysis → Context7 Pattern Research → Work Specification
```

1. **Requirement Parsing**: Zen MCP analyzes the development request for:
   - Core functionality requirements
   - Technical complexity assessment  
   - Potential implementation approaches
   - Quality and security considerations

2. **Pattern Research**: Context7 MCP investigates:
   - Existing implementation patterns
   - Library and framework options
   - Best practices and recommendations
   - Similar functionality in codebase

3. **Work Specification Creation**: Generate enriched specification containing:
   - Structured requirements and acceptance criteria
   - Technical implementation guidance
   - Research findings and pattern recommendations
   - Integration points and dependencies

### Phase 2: Agent Orchestration
```
Work Specification → Agent Assignment → Context Enhancement → Development Execution
```

1. **Agent Assignment**: Determine optimal agent(s):
   - **Simple tasks**: Direct assignment to James (Developer)
   - **Complex tasks**: Multi-agent coordination with specialized focus areas
   - **Critical tasks**: Enhanced review and validation pipelines

2. **Context Enhancement**: Enrich agent context with:
   - Work specification and requirements
   - Research findings and pattern recommendations
   - Implementation guidance and quality requirements
   - Integration context and dependency information

3. **Workflow State Management**: Track and manage:
   - Current development phase and progress
   - Agent assignments and transitions
   - Context preservation across handoffs
   - Quality gate completions

### Phase 3: Quality & Integration
```
Implementation → Enhanced Review → Change Implementation → Integration
```

1. **Enhanced Review Process**: 
   - **MCP-Enhanced Analysis**: Use Zen tools for multi-model code review
   - **Pattern Validation**: Verify adherence to researched best practices
   - **Quality Standards**: Ensure BMAD and LEVER framework compliance

2. **Intelligent Change Implementation**:
   - **Context-Aware Changes**: Use enriched context for implementing feedback
   - **Pattern-Based Solutions**: Leverage researched patterns for improvements
   - **Quality Validation**: Continuous validation against specifications

## Command Parameters

### Basic Parameters
- `--priority`: Task priority (low, medium, high, critical) - default: medium
- `--focus`: Development focus area (security, performance, ui, api, data) - default: general
- `--agent`: Preferred agent assignment (auto, james, multi) - default: auto
- `--style`: Development style (minimal, standard, comprehensive) - default: standard

### Advanced Parameters
- `--research-depth`: Context7 research depth (quick, standard, deep) - default: standard
- `--analysis-mode`: Zen analysis complexity (basic, standard, comprehensive) - default: standard
- `--quality-level`: Review intensity (standard, enhanced, strict) - default: standard
- `--pattern-search`: Pattern research scope (local, project, external) - default: project

### Status and Management
- `--status`: Show active development workflow status
- `--pause`: Pause current development workflow
- `--resume`: Resume paused development workflow
- `--cancel`: Cancel current development workflow

## Integration Points

### MCP Tool Integration
- **Zen MCP**: Multi-model analysis for requirement parsing and validation
- **Context7 MCP**: Pattern research and documentation retrieval
- **Enhanced Orchestration**: Intelligent agent coordination and handoff management

### Hook System Integration
- **Enhanced Orchestration Hook**: Automatic workflow triggering and management
- **Development Workflow Enhancer**: Context enrichment and state management
- **Quality Gate Integration**: Seamless integration with review and validation hooks

### State Management Integration
- **Workflow State**: Persistent state management for complex development cycles
- **Context Preservation**: Rich context maintained across agent transitions
- **Recovery Mechanisms**: Robust error handling and recovery processes

## Work Specification Structure

Generated work specifications include:

### Requirements Section
- **Primary Objective**: Core functionality to implement
- **Acceptance Criteria**: Specific, testable requirements
- **Quality Requirements**: Performance, security, maintainability standards
- **Integration Requirements**: Dependencies and integration points

### Technical Context
- **Research Findings**: Context7 pattern research results
- **Implementation Guidance**: Recommended approaches and frameworks
- **Architecture Considerations**: Design patterns and structural requirements
- **Dependency Analysis**: Required libraries and components

### Quality Assurance
- **Testing Strategy**: Recommended testing approaches and coverage
- **Review Criteria**: Specific review focuses and quality gates
- **Performance Requirements**: Performance benchmarks and constraints
- **Security Considerations**: Security requirements and validation criteria

### Implementation Plan
- **Task Breakdown**: Structured task decomposition
- **Agent Assignment**: Recommended agent allocation and responsibilities
- **Timeline Estimates**: Complexity-based time estimation
- **Risk Assessment**: Potential challenges and mitigation strategies

## Error Handling

### Requirement Analysis Failures
- **Incomplete Requirements**: Prompt for clarification and additional context
- **Technical Complexity**: Escalate to architect or break down into phases
- **Resource Conflicts**: Coordinate with existing workflows and dependencies

### Pattern Research Failures
- **No Patterns Found**: Proceed with minimal implementation approach
- **Conflicting Patterns**: Use Zen consensus tools for decision making
- **Integration Issues**: Research integration patterns and compatibility

### Workflow Orchestration Failures
- **Agent Unavailability**: Reassign to available agents or queue
- **Context Loss**: Restore from state management and re-enrich
- **Quality Gate Failures**: Trigger enhanced review and change implementation

## Quality Assurance Framework

### LEVER Framework Compliance
- **L**everage: Always research existing patterns before creating new code
- **E**xtend: Prioritize extending existing functionality over new implementation
- **V**erify: Use MCP tools for continuous verification and validation
- **E**liminate: Detect and eliminate duplication through pattern research
- **R**educe: Minimize complexity through intelligent analysis and design

### Development Quality Standards
- **Code Quality**: Adherence to BMAD coding standards and best practices
- **Testing Coverage**: Comprehensive testing strategy and implementation
- **Security Standards**: Security-first development with validation
- **Performance Standards**: Performance considerations and optimization

### Review Quality Enhancement
- **Multi-Model Validation**: Use Zen tools for enhanced code review
- **Pattern Validation**: Verify adherence to researched best practices
- **Context-Aware Review**: Review with full context of requirements and patterns
- **Continuous Improvement**: Learn from each workflow to enhance future development

## Examples

### Simple Feature Implementation
```bash
/dev "add password reset functionality"
```
- Zen MCP analyzes security and UX requirements
- Context7 researches password reset patterns and libraries
- Creates work specification with security best practices
- Assigns to James with enhanced context
- Triggers security-focused review process

### Complex Feature Development
```bash
/dev "implement real-time chat system with message persistence" --priority=high --focus=performance
```
- Comprehensive requirement analysis for real-time systems
- Research WebSocket libraries and message queue patterns
- Multi-agent coordination for frontend, backend, and infrastructure
- Performance-focused quality gates and optimization requirements
- Enhanced review process with performance validation

### API Development
```bash
/dev "create RESTful API for user management" --style=comprehensive
```
- API design pattern research and best practices
- OpenAPI specification integration and documentation
- Comprehensive testing strategy including API testing
- Security validation and authentication integration
- Enhanced documentation and integration testing

## Related Commands
- `/pm-orchestrate` - Project management orchestration for larger initiatives
- `/sub-agent-coordination` - Multi-agent coordination for complex features
- `/codereview` - Enhanced code review with MCP tool integration
- `/architect-review` - Architectural assessment and validation

---
*Part of the BMAD Method's intelligent development workflow system*