---
description: Review infrastructure design and implementation using Architect expertise for optimization
---

# BMAD Review Infrastructure Command

**Usage**: `*review-infrastructure [scope] [options]`

## Purpose

Reviews infrastructure design and implementation using Architect expertise, leveraging BMAD principles to assess existing infrastructure patterns while identifying opportunities to optimize and extend current capabilities.

### Key Benefits
- Leverages existing infrastructure documentation and operational knowledge
- Extends current infrastructure assessment with comprehensive review methodology
- Reduces infrastructure risk through systematic review and optimization
- Integrates seamlessly with BMAD infrastructure planning and deployment workflows

### When to Use
- After infrastructure architecture or deployment plans are created
- When evaluating existing infrastructure for optimization opportunities
- As part of infrastructure health checks and performance assessments
- When planning infrastructure changes or technology upgrades

## Implementation

### Step-by-Step Algorithm

#### 1. LEVER Framework Pre-Validation
Before executing, validate these principles:
- **L** - Leverage: Existing infrastructure documentation, monitoring data, and operational insights
- **E** - Extend: Current infrastructure assessment with comprehensive review capabilities
- **V** - Verify: Infrastructure performance against operational requirements and best practices
- **E** - Eliminate: Infrastructure inefficiencies and unnecessary complexity
- **R** - Reduce: Infrastructure operational overhead while maximizing reliability and performance

#### 2. Context Gathering
```bash
# Load infrastructure and operational context
CURRENT_AGENT=$(cat .claude/current-workflow-state.json | jq -r '.currentAgent // "architect"')
PROJECT_CONTEXT=$(cat .ai/project-context.md 2>/dev/null || echo "No project context available")
INFRASTRUCTURE_ARCH=$(cat docs/infrastructure-architecture.md 2>/dev/null || echo "No infrastructure architecture found")
DEPLOYMENT_PLAN=$(cat docs/deployment-plan.md 2>/dev/null || echo "No deployment plan found")
SYSTEM_ARCHITECTURE=$(cat docs/architecture.md 2>/dev/null || echo "No system architecture found")
```

#### 3. Agent-Specific Processing
Adapt behavior based on active agent persona:

##### If Architect Active
- Focus on comprehensive infrastructure architecture review and optimization
- System integration assessment and architectural alignment evaluation
- Security architecture and compliance framework review

##### If DevOps Active  
- Emphasize operational infrastructure assessment and deployment optimization
- Monitoring, alerting, and incident response capability review
- Automation and infrastructure as code evaluation

#### 4. Core Processing
- Load Architect persona and infrastructure review task
- Analyze existing infrastructure documentation and implementation
- Create comprehensive infrastructure review using established framework
- Document findings, optimization opportunities, and improvement recommendations
- Generate infrastructure improvement roadmap and implementation priorities

#### 5. Output Generation
- Structured infrastructure review saved to `docs/infrastructure-review.md`
- Infrastructure optimization recommendations and improvement matrix
- Performance assessment and capacity planning documentation
- Risk assessment and mitigation strategy recommendations

#### 6. Workflow Integration
- Updates workflow state to indicate infrastructure review completion
- Prepares context for infrastructure optimization and improvement planning
- Integrates with operational excellence and platform management workflows

## Quality Features / Validation Checklist

### BMAD Compliance Validation
- [ ] **LEVER Principles**: Leverages existing infrastructure knowledge and operational data
- [ ] **Agent Alignment**: Uses Architect or DevOps infrastructure expertise
- [ ] **Workflow Integration**: Properly follows infrastructure assessment workflow
- [ ] **Knowledge Capture**: Documents review findings and optimization opportunities
- [ ] **Context Awareness**: Builds upon existing infrastructure and operational context

### Technical Quality Gates
- [ ] **Input Validation**: Handles missing infrastructure documentation gracefully
- [ ] **Error Handling**: Provides guidance when infrastructure data is incomplete
- [ ] **Performance**: Efficient infrastructure review and assessment process
- [ ] **Consistency**: Maintains coherent review criteria and evaluation methodology
- [ ] **Documentation**: Clear, actionable infrastructure improvement recommendations

### Output Quality Assurance
- [ ] **Completeness**: Covers all infrastructure components and operational aspects
- [ ] **Accuracy**: Infrastructure assessment is based on current and relevant data
- [ ] **Relevance**: Review aligns with operational requirements and business objectives
- [ ] **Actionability**: Provides specific infrastructure optimization and improvement strategies
- [ ] **Integration**: Seamlessly connects with existing infrastructure and operational documentation

### Knowledge Management Standards
- [ ] **Persistence**: Saves infrastructure review to `docs/infrastructure-review.md`
- [ ] **Versioning**: Maintains infrastructure assessment history and improvement tracking
- [ ] **Cross-Reference**: Links to infrastructure architecture and operational documentation
- [ ] **Searchability**: Structured for easy reference during improvement planning
- [ ] **Agent Context**: Updates Architect and operations team infrastructure knowledge

## Integration

### Workflow Hooks
- **Triggers**: `read-workflow.sh` for LEVER principles reminder
- **Updates**: `workflow-transition.sh` for infrastructure review completion
- **Notifications**: `notification-hook.sh` for infrastructure review alerts
- **Context**: Updates `.claude/current-workflow-state.json`

### Knowledge Management Integration
- **Saves**: Infrastructure review to `docs/infrastructure-review.md`
- **Updates**: Project context with infrastructure assessment in `.ai/project-context.md`
- **Creates**: Infrastructure improvement tracking in `.ai/infrastructure-improvements/` directory
- **Links**: Cross-references with infrastructure architecture and operational documentation

### Agent Context Updates
- **Architect**: Updates infrastructure assessment knowledge and optimization strategies
- **All Agents**: Updates shared project infrastructure status and improvement context
- **DevOps**: Provides operational infrastructure assessment and optimization priorities
- **Platform Engineer**: Provides platform-level infrastructure review and enhancement opportunities

### Follow-up Command Preparation
Results prepare context for:
- `*validate-infrastructure` - Infrastructure validation based on review findings
- `*platform-change-management` - Infrastructure changes and improvement implementation
- `*create-deployment-plan` - Updated deployment planning based on review insights
- `*update-knowledge` - Distribute infrastructure review findings to all agents

## Related Commands

### Core Infrastructure Commands
- `*create-infrastructure-architecture` - Infrastructure architecture creation and updates
- `*validate-infrastructure` - Infrastructure validation and feasibility assessment
- `*platform-change-management` - Infrastructure improvement implementation

### Workflow Commands
- `*create-deployment-plan` - Deployment planning with infrastructure optimizations
- `*plan-workflow` - Infrastructure improvement workflow planning
- `*correct-course` - Strategic pivots based on infrastructure assessment

### Agent Commands
- `/switch-agent architect` - Optimal agent for comprehensive infrastructure review
- `/switch-agent devops` - Operational infrastructure assessment focus

### Knowledge Commands
- `/update-knowledge` - Distribute infrastructure review findings to all agents
- `/memory-extract` - Extract infrastructure insights and operational knowledge

## Example Usage

### Basic Usage
```bash
# Review current project infrastructure
*review-infrastructure
```

### Advanced Usage
```bash
# Review specific infrastructure components with optimization focus
*review-infrastructure networking --optimization-focus --performance-analysis
```

### Agent-Specific Usage
```bash
# When Architect agent is active:
*review-infrastructure --comprehensive --architecture-alignment

# When DevOps agent is active:
*review-infrastructure --operational --automation-assessment
```

### Workflow Integration Example
```bash
# Part of infrastructure optimization workflow:
/create-infrastructure-architecture
*review-infrastructure  # ← Comprehensive infrastructure review
/validate-infrastructure  # Validation based on review
/platform-change-management  # Implement improvements
/update-knowledge
```

## Notes

### Agent Personas
This command adapts its behavior based on the active agent:
- **Architect**: Comprehensive infrastructure architecture review with optimization focus
- **DevOps**: Operational infrastructure assessment with automation and monitoring focus
- **Platform Engineer**: Platform-level infrastructure review with self-service capabilities assessment
- **Security**: Security-focused infrastructure review with compliance and threat assessment
- **QA**: Infrastructure testing and validation procedures review

### LEVER Framework Emphasis
Every execution must demonstrate:
- **Leverage**: Existing infrastructure documentation, monitoring data, and operational insights
- **Extend**: Current infrastructure assessment capabilities with comprehensive review methodology
- **Verify**: Infrastructure performance against operational requirements and industry best practices
- **Eliminate**: Infrastructure inefficiencies, redundancies, and unnecessary operational complexity
- **Reduce**: Infrastructure management overhead while maximizing system reliability and performance

### Best Practices
- Include both technical architecture and operational procedures in the review
- Focus on optimization opportunities that provide clear operational value
- Document risk assessment and mitigation strategies for identified issues
- Validate review findings against current operational metrics and performance data