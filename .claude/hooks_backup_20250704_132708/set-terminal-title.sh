#!/bin/bash

# Set Terminal Title Hook for Claude Code
# Sets the terminal title to include the current project name
# This helps identify which project is being worked on

# Read JSON input from stdin
JSON_INPUT=$(cat)

# Get current directory and extract project name
CURRENT_DIR=$(pwd)
PROJECT_NAME=$(basename "$CURRENT_DIR")

# If we're in a subdirectory, try to find the project root
if [[ "$CURRENT_DIR" == *"/.claude"* ]] || [[ "$CURRENT_DIR" == */bmad-agent/* ]]; then
    # Extract project root from path
    PROJECT_ROOT="${CURRENT_DIR%%/.claude*}"
    PROJECT_ROOT="${PROJECT_ROOT%%/bmad-agent*}"
    PROJECT_NAME=$(basename "$PROJECT_ROOT")
fi

# Set terminal title using ANSI escape sequences
# \033]0; sets both window and tab title
# \007 ends the sequence
echo -ne "\033]0;<PERSON> Code - $PROJECT_NAME\007"

# Also display project info
echo "🏗️  Working on project: $PROJECT_NAME"
echo "📁 Path: $CURRENT_DIR"

# Pass through the JSON for the next hook/tool
echo "$JSON_INPUT"

# Always exit 0 - this is informational only
exit 0