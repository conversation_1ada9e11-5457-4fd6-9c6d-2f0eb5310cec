#!/bin/bash

# Workflow Status Display Hook
# Shows the current workflow phase and history visually

# Trap SIGPIPE to prevent errors
trap '' PIPE

# Read JSON input from stdin
if ! JSON_INPUT=$(cat 2>/dev/null); then
    exit 0
fi

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CLAUDE_DIR="$(dirname "$SCRIPT_DIR")"

WORKFLOW_STATE="$CLAUDE_DIR/current-workflow-state.json"
WORKFLOW_LOG="$CLAUDE_DIR/workflow.log"

# Function to get emoji for stage
get_stage_emoji() {
    case "$1" in
        "development") echo "💻" ;;
        "review") echo "🔍" ;;
        "implementation") echo "🔨" ;;
        *) echo "❓" ;;
    esac
}

# Function to get status color
get_stage_color() {
    case "$1" in
        "development") echo "\033[1;36m" ;;      # <PERSON>an
        "review") echo "\033[1;33m" ;;           # Yellow
        "implementation") echo "\033[1;32m" ;;   # Green
        *) echo "\033[0m" ;;
    esac
}

# Read current workflow state
if [ -f "$WORKFLOW_STATE" ]; then
    CURRENT_STAGE=$(jq -r '.currentStage // "development"' "$WORKFLOW_STATE" 2>/dev/null)
    TRANSITION_COUNT=$(jq -r '.transitionCount // 0' "$WORKFLOW_STATE" 2>/dev/null)
    START_TIME=$(jq -r '.startTime // ""' "$WORKFLOW_STATE" 2>/dev/null)
else
    CURRENT_STAGE="development"
    TRANSITION_COUNT=0
    START_TIME=""
fi

# Extract tool name for context
TOOL_NAME=$(echo "$JSON_INPUT" | jq -r '.toolName // empty' 2>/dev/null)

# Only show workflow status for relevant tools
if [[ "$TOOL_NAME" =~ ^(Write|Edit|MultiEdit|Read|TodoWrite)$ ]]; then
    
    # Clear line and show workflow status
    echo -e "\033[2K\r"  # Clear current line
    
    # Build workflow progress bar
    DEV_COLOR=$([[ "$CURRENT_STAGE" == "development" ]] && echo "\033[1;42m" || echo "\033[1;44m")  # Green bg if active, blue if done
    REV_COLOR=$([[ "$CURRENT_STAGE" == "review" ]] && echo "\033[1;43m" || echo "\033[1;40m")      # Yellow bg if active, black if not
    IMP_COLOR=$([[ "$CURRENT_STAGE" == "implementation" ]] && echo "\033[1;42m" || echo "\033[1;40m") # Green bg if active, black if not
    
    # Display workflow header
    echo -e "\033[1;35m┌─────────────────── WORKFLOW STATUS ───────────────────┐\033[0m"
    
    # Show progress bar
    echo -ne "\033[1;35m│\033[0m "
    echo -ne "${DEV_COLOR} DEV \033[0m"
    echo -ne " → "
    echo -ne "${REV_COLOR} REVIEW \033[0m"
    echo -ne " → "
    echo -ne "${IMP_COLOR} IMPLEMENT \033[0m"
    echo -e " \033[1;35m│\033[0m"
    
    # Show current stage details
    STAGE_COLOR=$(get_stage_color "$CURRENT_STAGE")
    STAGE_EMOJI=$(get_stage_emoji "$CURRENT_STAGE")
    
    echo -e "\033[1;35m│\033[0m                                                       \033[1;35m│\033[0m"
    echo -e "\033[1;35m│\033[0m ${STAGE_COLOR}Current Stage:${STAGE_EMOJI} ${CURRENT_STAGE^^}\033[0m"
    echo -e "\033[1;35m│\033[0m Transitions: $TRANSITION_COUNT                              \033[1;35m│\033[0m"
    
    # Show stage-specific message
    case "$CURRENT_STAGE" in
        "development")
            echo -e "\033[1;35m│\033[0m \033[1;36m📝 Writing code - Remember LEVER principles!\033[0m         \033[1;35m│\033[0m"
            ;;
        "review")
            echo -e "\033[1;35m│\033[0m \033[1;33m🔍 Review mode - Check LEVER compliance!\033[0m            \033[1;35m│\033[0m"
            ;;
        "implementation")
            echo -e "\033[1;35m│\033[0m \033[1;32m🔨 Implementing changes from review!\033[0m                \033[1;35m│\033[0m"
            ;;
    esac
    
    echo -e "\033[1;35m└───────────────────────────────────────────────────────┘\033[0m"
    
    # Show recent transitions from log
    if [ -f "$WORKFLOW_LOG" ] && [ "$TRANSITION_COUNT" -gt 0 ]; then
        echo -e "\033[1;90mRecent transitions:\033[0m"
        tail -3 "$WORKFLOW_LOG" | while read -r line; do
            echo -e "\033[90m  • $line\033[0m"
        done
    fi
    
    echo ""  # Blank line for separation
fi

# Pass through the JSON
{ echo "$JSON_INPUT"; } 2>/dev/null || true

exit 0