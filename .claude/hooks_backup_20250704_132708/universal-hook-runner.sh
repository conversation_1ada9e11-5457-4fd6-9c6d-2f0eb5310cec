#!/bin/bash

# Universal Hook Runner - Works in ANY project, ANY directory
# This script dynamically finds the .claude directory and runs hooks
# No hardcoded paths - truly portable across all projects

# Set error handling
set -o pipefail
trap '' PIPE

# Function to find .claude directory from current location
find_project_claude_dir() {
    local search_dir="$PWD"
    
    # Walk up directory tree until we find .claude or reach root
    while [ "$search_dir" != "/" ]; do
        if [ -d "$search_dir/.claude" ]; then
            echo "$search_dir/.claude"
            return 0
        fi
        search_dir=$(dirname "$search_dir")
    done
    
    # Not found
    return 1
}

# Get the hook name from command line argument
HOOK_NAME="$1"
if [ -z "$HOOK_NAME" ]; then
    echo "Error: Hook name required" >&2
    exit 2
fi
shift  # Remove hook name from arguments

# Find the .claude directory for THIS project
CLAUDE_DIR=$(find_project_claude_dir)
if [ -z "$CLAUDE_DIR" ]; then
    echo "Error: Cannot find .claude directory from $(pwd)" >&2
    exit 2
fi

# Construct hook path
HOOK_SCRIPT="$CLAUDE_DIR/hooks/$HOOK_NAME"

# Verify hook exists and is executable
if [ ! -f "$HOOK_SCRIPT" ]; then
    echo "Error: Hook not found: $HOOK_SCRIPT" >&2
    exit 2
fi

if [ ! -x "$HOOK_SCRIPT" ]; then
    echo "Error: Hook not executable: $HOOK_SCRIPT" >&2
    exit 2
fi

# Execute the hook with stdin passthrough
# This is critical for Claude Code JSON data flow
cat | "$HOOK_SCRIPT" "$@"
hook_exit_code=$?

# Optional: Debug output to stderr (won't interfere with JSON)
# echo "Executed: $HOOK_NAME from $CLAUDE_DIR (exit: $hook_exit_code)" >&2

exit $hook_exit_code