#!/bin/bash

# LEVER Framework Planning Reminder
# Claude Code compatible hook that displays LEVER principles when entering planning mode

# Read JSON input from stdin (required for Claude Code hooks)
JSON_INPUT=$(cat)

# Extract tool name
TOOL_NAME=$(echo "$JSON_INPUT" | jq -r '.toolName // empty' 2>/dev/null)

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CLAUDE_DIR="$(dirname "$SCRIPT_DIR")"

# Create a planning reminder file
PLANNING_REMINDER="$CLAUDE_DIR/lever-planning-reminder.md"

# Generate the reminder
cat > "$PLANNING_REMINDER" << 'EOF'
# 🎯 LEVER Framework Planning Reminder

## Remember: "The best code is no code. The second best code is code that already exists and works."

### When Planning, Always Consider:

#### L - Leverage Existing Patterns
- What similar functionality already exists in the codebase?
- Can we reuse existing components, queries, or hooks?
- Are there patterns we can follow from other features?

#### E - Extend Before Creating
- Can we extend an existing table instead of creating a new one?
- Can we add fields to existing queries?
- Can we enhance existing hooks with new computed properties?
- Can we modify existing components with conditional rendering?

#### V - Verify Through Reactivity
- How can we use reactive patterns to minimize code?
- Can computed values replace manual state management?
- Are we leveraging the framework's reactivity effectively?

#### E - Eliminate Duplication
- Are we creating something that already exists?
- Can we abstract common functionality?
- Are we repeating patterns that could be shared?

#### R - Reduce Complexity
- What's the simplest solution that works?
- Are we over-engineering?
- Can we solve this with configuration instead of code?

### Quick Decision Checklist:
1. ✅ Can I extend an existing table? (vs creating new)
2. ✅ Can I add to an existing query? (vs new query)
3. ✅ Can I enhance an existing hook? (vs new hook)
4. ✅ Can I modify an existing component? (vs new component)

### Planning Questions to Ask:
- What's the minimal code needed to achieve the goal?
- Which existing code can I build upon?
- How can I avoid creating new files/tables/endpoints?
- What would be the "no code" solution to this problem?
EOF

# Display the reminder in console
echo "=========================================="
echo "    🎯 LEVER FRAMEWORK PLANNING MODE"
echo "=========================================="
echo ""
echo "Remember: The best code is no code!"
echo ""
echo "LEVER Principles for Planning:"
echo "  L - Leverage existing patterns"
echo "  E - Extend before creating"
echo "  V - Verify through reactivity"
echo "  E - Eliminate duplication"
echo "  R - Reduce complexity"
echo ""
echo "Before planning any implementation:"
echo "  1. Search for existing similar functionality"
echo "  2. Consider extending vs creating new"
echo "  3. Think about the simplest solution"
echo "  4. Avoid unnecessary complexity"
echo ""
echo "Full reminder saved to: lever-planning-reminder.md"
echo "=========================================="

# Send notification
osascript -e 'display notification "LEVER principles active for planning" with title "BMAD Planning Mode" sound name "Purr"' 2>/dev/null || true


# Pass through the JSON for the next hook/tool
echo "$JSON_INPUT"
exit 0