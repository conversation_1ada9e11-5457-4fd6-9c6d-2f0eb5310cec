#!/bin/bash

# Hook script to handle workflow stage transitions
# Claude Code compatible - reads <PERSON><PERSON><PERSON> from stdin

# Read JSON input from stdin (required for Claude Code hooks)
JSON_INPUT=$(cat)

# Extract tool name and transition type from JSON if available
TOOL_NAME=$(echo "$JSON_INPUT" | jq -r '.toolName // empty' 2>/dev/null)

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CLAUDE_DIR="$(dirname "$SCRIPT_DIR")"

WORKFLOW_STATE="$CLAUDE_DIR/current-workflow-state.json"
WORKFLOW_CONFIG="$CLAUDE_DIR/workflow-config.json"
WORKFLOW_LOG="$CLAUDE_DIR/workflow.log"

# Function to log workflow events
log_workflow() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1" >> "$WORKFLOW_LOG"
}

# Function to send notification (with error suppression for non-macOS)
notify() {
    local title="$1"
    local message="$2"
    local sound="${3:-Ping}"
    osascript -e "display notification \"$message\" with title \"$title\" sound name \"$sound\"" 2>/dev/null || true
}

# Function to get current stage
get_current_stage() {
    if [ -f "$WORKFLOW_STATE" ]; then
        jq -r '.currentStage // "pre-implementation"' "$WORKFLOW_STATE"
    else
        echo "pre-implementation"
    fi
}

# Function to display stage transition info
display_stage_info() {
    local from_stage="$1"
    local to_stage="$2"
    
    echo ""
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "                    🔄 WORKFLOW STAGE TRANSITION"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo ""
    echo "  From: $from_stage"
    echo "  To:   $to_stage"
    echo ""
    
    case "$to_stage" in
        "development")
            echo "📝 DEVELOPMENT STAGE:"
            echo "  • Focus on implementing with LEVER principles"
            echo "  • Extend existing code where possible"
            echo "  • Minimize new code creation"
            ;;
        "review")
            echo "🔍 REVIEW STAGE:"
            echo "  • Switch to REVIEWER role"
            echo "  • Check LEVER compliance"
            echo "  • Verify code extends existing functionality"
            echo "  • Identify simplification opportunities"
            ;;
        "rewrite")
            echo "🔧 REWRITE STAGE:"
            echo "  • Address review feedback"
            echo "  • Focus on LEVER violations identified"
            echo "  • Simplify and extend existing code"
            ;;
        "complete")
            echo "✅ COMPLETE:"
            echo "  • Task successfully completed"
            echo "  • LEVER principles followed"
            echo "  • Code review passed"
            ;;
    esac
    
    echo ""
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo ""
}

# Main logic - automatic stage detection based on current state and tool
CURRENT_STAGE=$(get_current_stage)

# Determine next stage based on current stage and context
case "$CURRENT_STAGE" in
    "pre-implementation")
        # Moving to development
        NEXT_STAGE="development"
        display_stage_info "$CURRENT_STAGE" "$NEXT_STAGE"
        notify "BMAD Development" "Starting development phase" "Hero"
        ;;
    "development")
        # Moving to review after development
        NEXT_STAGE="review"
        display_stage_info "$CURRENT_STAGE" "$NEXT_STAGE"
        notify "BMAD Code Review" "Switch to REVIEWER role" "Glass"
        ;;
    "review")
        # Could go to complete or rewrite - display options
        echo ""
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo "                    📋 REVIEW DECISION NEEDED"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        echo "After review, decide:"
        echo "  ✅ If LEVER compliant → Mark as COMPLETE"
        echo "  🔄 If changes needed → Return to REWRITE stage"
        echo ""
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        NEXT_STAGE="review"  # Stay in review
        ;;
    "rewrite")
        # After rewrite, go back to review
        NEXT_STAGE="review"
        display_stage_info "$CURRENT_STAGE" "$NEXT_STAGE"
        notify "BMAD Review" "Rewrite complete - Review again" "Glass"
        ;;
    *)
        NEXT_STAGE="pre-implementation"
        ;;
esac

# Update workflow state if changing
if [ "$NEXT_STAGE" != "$CURRENT_STAGE" ]; then
    if [ -f "$WORKFLOW_STATE" ]; then
        # Get current transition count
        TRANSITION_COUNT=$(jq -r '.transitionCount // 0' "$WORKFLOW_STATE" 2>/dev/null)
        TRANSITION_COUNT=$((TRANSITION_COUNT + 1))
        
        # Update existing state
        jq ".currentStage = \"$NEXT_STAGE\" | .lastTransition = \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\" | .transitionCount = $TRANSITION_COUNT" "$WORKFLOW_STATE" > "$WORKFLOW_STATE.tmp" && mv "$WORKFLOW_STATE.tmp" "$WORKFLOW_STATE"
    else
        # Create new state
        echo "{
  \"workflow\": \"dev-review-rewrite\",
  \"currentStage\": \"$NEXT_STAGE\",
  \"iteration\": 1,
  \"transitionCount\": 0,
  \"startTime\": \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\",
  \"lastTransition\": \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\"
}" > "$WORKFLOW_STATE"
    fi
    
    log_workflow "Transitioned from $CURRENT_STAGE to $NEXT_STAGE (Total transitions: $TRANSITION_COUNT)"
    
    # Send notification for workflow transitions
    PROJECT_NAME=$(basename "$(pwd)")
    case "$NEXT_STAGE" in
        "review")
            osascript -e "display notification \"Developer has handed off code for review\" with title \"BMAD Workflow - $PROJECT_NAME\" sound name \"Ping\"" 2>/dev/null || true
            echo "🔄 Workflow: Developer → Reviewer"
            ;;
        "implementation"|"pre-implementation")
            osascript -e "display notification \"Reviewer completed - ready for implementation\" with title \"BMAD Workflow - $PROJECT_NAME\" sound name \"Hero\"" 2>/dev/null || true
            echo "🔄 Workflow: Reviewer → Implementation"
            ;;
        "development")
            if [ "$CURRENT_STAGE" = "review" ]; then
                osascript -e "display notification \"Review completed - back to development\" with title \"BMAD Workflow - $PROJECT_NAME\" sound name \"Blow\"" 2>/dev/null || true
                echo "🔄 Workflow: Reviewer → Developer (fixes needed)"
            else
                osascript -e "display notification \"New development cycle started\" with title \"BMAD Workflow - $PROJECT_NAME\" sound name \"Glass\"" 2>/dev/null || true
                echo "🔄 Workflow: New development cycle"
            fi
            ;;
    esac
fi


# Pass through the JSON for the next hook/tool
echo "$JSON_INPUT"
exit 0