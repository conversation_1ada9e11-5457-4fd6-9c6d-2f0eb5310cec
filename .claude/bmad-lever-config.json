{"leverTriggers": {"planningCommands": ["*plan-workflow", "/plan-workflow", "*SM create", "*SM create-with-orchestration", "*Architect Create Architecture", "*Design Architect Create Frontend Architecture", "*DevOps infra-plan", "*Architect module-design", "*Platform Engineer design"], "description": "BMAD commands that should trigger LEVER planning reminders"}, "leverIntegration": {"claudePlanMode": true, "bmadCommands": true, "autoReminder": true}}