---
description: Comprehensive security audit following OWASP standards and BMAD security principles
---

# BMAD Security Audit Command

**Usage**: `*secaudit [scope] [security-focus]`

## Purpose

Perform comprehensive security assessment following OWASP Top 10, industry best practices, and BMAD security principles with agent-specific security expertise.

## LEVER Framework Security Analysis

Before security audit, establish security principles:
- **L** - Leverage: What existing security patterns, tools, and frameworks can we use?
- **E** - Extend: How can we enhance existing security measures rather than replacing them?
- **V** - Verify: How can we validate security through existing testing and monitoring?
- **E** - Eliminate: What security complexity and attack surface can we remove?
- **R** - Reduce: What's the simplest security approach that provides adequate protection?

## Agent-Specific Security Focus

### Current Agent Context
Adapt security assessment based on active agent persona:

#### If Security Agent Active
- Focus on comprehensive vulnerability assessment and threat modeling
- Analyze authentication, authorization, and access control mechanisms
- Assess cryptographic implementations and data protection
- Review security architecture and defense-in-depth strategies

#### If Developer Agent Active
- Focus on secure coding practices and implementation vulnerabilities
- Analyze input validation and output encoding
- Assess error handling and information disclosure
- Review dependency security and supply chain risks

#### If Architect Agent Active
- Focus on security architecture and system-level protections
- Analyze security boundaries and trust relationships
- Assess scalability of security measures
- Review integration security and API protection

#### If DevOps Agent Active
- Focus on infrastructure security and deployment protection
- Analyze container security and orchestration
- Assess monitoring, logging, and incident response
- Review CI/CD pipeline security and secrets management

## Comprehensive Security Assessment

### 1. Security Context Analysis
```bash
# Capture security audit session
echo "## Security Audit Session: $(date)" >> .ai/security-log.md
echo "### Scope: $SCOPE" >> .ai/security-log.md
echo "### Focus: $SECURITY_FOCUS" >> .ai/security-log.md
echo "### Agent Context: $CURRENT_AGENT" >> .ai/security-log.md
```

### 2. OWASP Top 10 Assessment

#### A01: Broken Access Control
- Authentication bypass vulnerabilities
- Authorization flaws and privilege escalation
- Directory traversal and file access issues
- Cross-Origin Resource Sharing (CORS) misconfigurations

#### A02: Cryptographic Failures
- Weak encryption algorithms and implementations
- Inadequate key management and storage
- Insecure data transmission and storage
- Password storage and hashing weaknesses

#### A03: Injection Vulnerabilities
- SQL injection and NoSQL injection
- Command injection and code injection
- LDAP injection and XPath injection
- Template injection and expression language injection

#### A04: Insecure Design
- Missing security controls in design
- Inadequate threat modeling
- Insufficient security requirements
- Weak security architecture patterns

#### A05: Security Misconfiguration
- Default configurations and credentials
- Unnecessary features and services enabled
- Missing security headers and protections
- Inadequate error handling and information disclosure

#### A06: Vulnerable and Outdated Components
- Known vulnerabilities in dependencies
- Outdated frameworks and libraries
- Unpatched systems and software
- End-of-life components and technologies

#### A07: Identification and Authentication Failures
- Weak password policies and requirements
- Inadequate session management
- Missing multi-factor authentication
- Brute force and credential stuffing vulnerabilities

#### A08: Software and Data Integrity Failures
- Unsigned or unverified software updates
- Insecure deserialization vulnerabilities
- Inadequate supply chain security
- Missing integrity verification

#### A09: Security Logging and Monitoring Failures
- Insufficient logging and monitoring
- Inadequate incident detection and response
- Missing audit trails and forensic capabilities
- Poor log protection and retention

#### A10: Server-Side Request Forgery (SSRF)
- SSRF vulnerabilities in web applications
- Inadequate input validation for URLs
- Missing network segmentation
- Insufficient allowlist/blocklist controls

### 3. LEVER Security Validation

#### Leverage Security Analysis
- Identify existing security frameworks and tools to utilize
- Assess current security patterns and practices
- Leverage established security libraries and components
- Utilize existing threat intelligence and monitoring

#### Extension Security Assessment
- Enhance existing authentication and authorization systems
- Extend current logging and monitoring capabilities
- Build upon established security policies and procedures
- Improve existing security testing and validation

#### Verification Security Methods
- Validate security through automated testing and scanning
- Verify security controls through penetration testing
- Test security measures through threat simulation
- Confirm security through compliance auditing

#### Elimination Security Priorities
- Remove unnecessary attack surface and complexity
- Eliminate unused features and endpoints
- Remove insecure legacy code and dependencies
- Eliminate overly permissive access controls

#### Reduction Security Strategies
- Implement minimal privilege principles
- Reduce complexity in security implementations
- Simplify authentication and authorization flows
- Minimize data collection and retention

### 4. Security Architecture Assessment

#### Defense in Depth
- Multiple layers of security controls
- Redundant security mechanisms
- Fail-safe and fail-secure design
- Security boundaries and compartmentalization

#### Zero Trust Principles
- Never trust, always verify approach
- Least privilege access controls
- Continuous verification and monitoring
- Microsegmentation and network isolation

#### Threat Modeling
- Asset identification and classification
- Threat actor analysis and capabilities
- Attack vector identification and assessment
- Risk prioritization and mitigation strategies

## Output Format

### Security Summary
- Overall security posture assessment
- Agent perspective and expertise applied
- OWASP Top 10 compliance evaluation
- Critical security findings and priorities

### Vulnerability Assessment
- Identified vulnerabilities with severity ratings
- CVSS scores and risk classifications
- Exploitation scenarios and impact analysis
- Evidence and proof-of-concept demonstrations

### Security Recommendations
- Priority-ranked security improvements
- LEVER-compliant security enhancements
- Specific remediation steps and timelines
- Security architecture improvements

### Implementation Plan
- Immediate critical fixes required
- Short-term security improvements
- Long-term security strategy enhancements
- Compliance and regulatory requirements

### Workflow Integration
- Update workflow state with security findings
- Document security decisions in knowledge base
- Create security task tracking and remediation
- Schedule security review and validation

## Integration with BMAD System

### Workflow Hooks
- Triggers `read-workflow.sh` for LEVER principles reminder
- Updates workflow state through `workflow-transition.sh`
- Logs security audit through `notification-hook.sh`

### Knowledge Management
- Saves security audit to `.ai/security/audit-[timestamp].md`
- Updates security posture and compliance status
- Creates vulnerability tracking and remediation plans
- Updates security guidelines and best practices

### Follow-up Commands
Security audit prepares context for:
- `*codereview` - Security-focused code review
- `*testgen` - Security test case generation
- `*analyze` - Detailed vulnerability analysis
- `*thinkdeep` - Strategic security planning

## Example Usage

```bash
# Comprehensive application security audit
*secaudit src/ application

# API security assessment
*secaudit src/api/ api-security

# Infrastructure security review
*secaudit infrastructure/ infra-security
```

## Quality Gates

All security audits must meet BMAD standards:
- **LEVER Compliance**: Demonstrates leverage, extension, verification, elimination, reduction
- **OWASP Compliance**: Addresses OWASP Top 10 and security best practices
- **Agent Alignment**: Respects current agent security expertise and focus
- **Risk-Based**: Prioritizes findings based on actual risk and impact
- **Actionable Output**: Provides specific, implementable security improvements
