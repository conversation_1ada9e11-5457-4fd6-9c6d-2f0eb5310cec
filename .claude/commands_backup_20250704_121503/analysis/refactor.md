---
description: Comprehensive refactoring analysis following BMAD principles and best practices
---

# BMAD Refactoring Command

**Usage**: `*refactor [scope] [refactor-type]`

## Purpose

Perform systematic refactoring analysis to improve code quality, maintainability, and architectural design while following BMAD principles and agent-specific expertise.

## LEVER Framework Refactoring Analysis

Before refactoring, establish improvement principles:
- **L** - Leverage: What existing patterns, libraries, and frameworks should we build upon?
- **E** - Extend: How can we enhance existing code rather than rewriting from scratch?
- **V** - Verify: How can we validate improvements through testing and metrics?
- **E** - Eliminate: What complexity, duplication, and technical debt can we remove?
- **R** - Reduce: What's the simplest approach that achieves our quality goals?

## Agent-Specific Refactoring Focus

### Current Agent Context
Adapt refactoring analysis based on active agent persona:

#### If Developer Agent Active
- Focus on code quality improvements and clean code principles
- Analyze function complexity and single responsibility adherence
- Assess variable naming, method organization, and readability
- Review error handling patterns and exception management

#### If Architect Agent Active
- Focus on structural improvements and design patterns
- Analyze architectural boundaries and separation of concerns
- Assess scalability implications of current design
- Review integration patterns and API design

#### If Performance Agent Active
- Focus on performance optimization opportunities
- Analyze algorithmic complexity and bottlenecks
- Assess memory usage patterns and optimization potential
- Review caching strategies and resource utilization

#### If Security Agent Active
- Focus on security improvement opportunities
- Analyze input validation and sanitization patterns
- Assess authentication and authorization implementations
- Review cryptographic usage and secure coding practices

## Refactoring Categories

### 1. Code Smells Detection

#### Bloater Smells
```bash
# Long Method Detection
find . -name "*.py" -o -name "*.js" -o -name "*.java" | xargs wc -l | sort -nr | head -20

# Large Class Detection
grep -r "^class\|^def\|^function" --include="*.py" --include="*.js" . | \
  awk '{print $1}' | sort | uniq -c | sort -nr | head -10
```

- **Long Method**: Methods with excessive lines of code
- **Large Class**: Classes with too many responsibilities
- **Primitive Obsession**: Overuse of primitive types instead of objects
- **Long Parameter List**: Methods with too many parameters
- **Data Clumps**: Groups of data that appear together frequently

#### Object-Orientation Abuser Smells
- **Switch Statements**: Excessive conditional logic that should be polymorphic
- **Temporary Field**: Fields that are only set in certain circumstances
- **Refused Bequest**: Subclasses that don't use inherited functionality
- **Alternative Classes**: Classes that do similar things with different interfaces

#### Change Preventer Smells
- **Divergent Change**: One class commonly changed for different reasons
- **Shotgun Surgery**: Changes require modifications across many classes
- **Parallel Inheritance**: Creating subclass forces creating subclass elsewhere

#### Dispensable Smells
- **Comments**: Excessive commenting indicating unclear code
- **Duplicate Code**: Identical or similar code in multiple locations
- **Lazy Class**: Classes that don't do enough to justify existence
- **Data Class**: Classes that only contain fields and accessor methods
- **Dead Code**: Unused variables, parameters, methods, or classes
- **Speculative Generality**: Unused abstract classes and unnecessary complexity

#### Coupler Smells
- **Feature Envy**: Methods that use other classes more than their own
- **Inappropriate Intimacy**: Classes that know too much about each other
- **Message Chains**: Long chains of method calls through objects
- **Middle Man**: Classes that delegate most work to other classes

### 2. Decomposition Opportunities

#### Function Decomposition
- Extract complex logic into smaller, focused functions
- Separate business logic from presentation concerns
- Create utility functions for common operations
- Split conditional logic into strategy patterns

#### Class Decomposition
- Split large classes using Single Responsibility Principle
- Extract interfaces for better testability and flexibility
- Create value objects for related data groups
- Separate data access from business logic

#### Module Decomposition
- Break large modules into focused, cohesive units
- Extract shared utilities into common modules
- Separate configuration from implementation
- Create clear API boundaries between modules

### 3. Modernization Assessment

#### Language Feature Upgrades
```bash
# Python modernization opportunities
grep -r "lambda.*:" --include="*.py" . | wc -l  # Lambda usage
grep -r "list(\|dict(\|set(" --include="*.py" . | wc -l  # Constructor usage
grep -r "%.format\|%" --include="*.py" . | wc -l  # String formatting
```

- **Python**: f-strings, type hints, dataclasses, async/await, match statements
- **JavaScript**: ES6+ features, async/await, destructuring, modules
- **Java**: Records, switch expressions, var keyword, streams API
- **C#**: Pattern matching, records, nullable reference types

#### Framework and Library Updates
- Migration to newer framework versions
- Adoption of more efficient libraries
- Replacement of deprecated APIs
- Integration of modern development tools

#### Architecture Modernization
- Microservices decomposition opportunities
- Cloud-native patterns adoption
- Event-driven architecture implementation
- API-first design principles

### 4. Organization Improvements

#### File and Directory Structure
```bash
# Analyze project structure
find . -type f -name "*.py" | head -20
tree -d -L 3 .
```

- Logical grouping of related functionality
- Clear separation of concerns in directory structure
- Consistent naming conventions
- Appropriate abstraction levels

#### Code Organization Patterns
- Package/namespace organization
- Import/dependency management
- Configuration and environment handling
- Testing structure and organization

### 5. LEVER Refactoring Validation

#### Leverage Analysis
- Identify existing patterns that should be reused
- Assess framework capabilities that could be better utilized
- Find opportunities to use established libraries
- Leverage existing testing and monitoring infrastructure

#### Extension Assessment
- Enhance existing functionality rather than replacing
- Build upon current architecture and patterns
- Extend existing APIs and interfaces
- Improve current error handling and logging

#### Verification Methods
- Maintain or improve test coverage during refactoring
- Verify performance improvements through benchmarks
- Validate architectural improvements through metrics
- Confirm maintainability through code quality tools

#### Elimination Priorities
- Remove duplicate code and redundant functionality
- Eliminate unnecessary complexity and over-engineering
- Remove dead code and unused dependencies
- Eliminate tight coupling and circular dependencies

#### Reduction Strategies
- Simplify complex algorithms and data structures
- Reduce cognitive load through clearer abstractions
- Minimize configuration and setup complexity
- Reduce maintenance burden through automation

## Output Format

### Refactoring Summary
- Overall code quality assessment
- Agent perspective and expertise applied
- Priority-ranked improvement opportunities
- LEVER compliance evaluation

### Code Smell Analysis
- Identified smells with severity and impact ratings
- Specific examples and evidence
- Refactoring effort estimation
- Risk assessment for proposed changes

### Decomposition Plan
- Recommended function/class/module splits
- Dependency analysis and impact assessment
- Migration strategy and implementation phases
- Testing strategy for refactored components

### Modernization Roadmap
- Language and framework upgrade opportunities
- Architecture improvement recommendations
- Tool and process modernization suggestions
- Timeline and resource requirements

### Implementation Strategy
- Step-by-step refactoring plan
- Risk mitigation and rollback strategies
- Testing and validation checkpoints
- Performance and quality metrics tracking

### Workflow Integration
- Update workflow state with refactoring plan
- Document architectural decisions in knowledge base
- Create refactoring task tracking and progress monitoring
- Schedule code reviews and quality assessments

## Integration with BMAD System

### Workflow Hooks
- Triggers `read-workflow.sh` for LEVER principles reminder
- Updates workflow state through `workflow-transition.sh`
- Logs refactoring analysis through `notification-hook.sh`

### Knowledge Management
- Saves refactoring plan to `.ai/refactoring/plan-[timestamp].md`
- Updates code quality metrics and technical debt tracking
- Creates refactoring task lists and progress tracking
- Updates coding standards and best practices documentation

### Follow-up Commands
Refactoring analysis prepares context for:
- `*codereview` - Quality-focused code review
- `*testgen` - Test coverage for refactored code
- `*analyze` - Detailed complexity analysis
- `*thinkdeep` - Strategic refactoring planning

## Example Usage

```bash
# Comprehensive code quality refactoring
*refactor src/ codesmells

# Decomposition analysis for large modules
*refactor src/core/ decompose

# Modernization assessment
*refactor . modernize

# Organization improvement analysis
*refactor . organization
```

## Quality Gates

All refactoring analysis must meet BMAD standards:
- **LEVER Compliance**: Demonstrates leverage, extension, verification, elimination, reduction
- **Quality Focus**: Prioritizes maintainability, readability, and testability
- **Agent Alignment**: Respects current agent expertise and perspective
- **Risk Assessment**: Evaluates impact and effort for proposed changes
- **Actionable Output**: Provides specific, implementable refactoring steps
