# Quick: Switch to Dev Mode

**Fastest way to switch to development mode for rapid coding.**

## Command
```bash
.claude/hooks/switch-mode.sh dev && echo "🔄 Now restart Claude Code to apply dev mode"
```

This switches to development mode which:
- Removes slow automatic lint
- Keeps LEVER workflow reminders  
- Much faster for active coding

## Manual Lint
```bash
.claude/hooks/manual-lint.sh
```

## Switch Back to Full Mode
```bash
.claude/hooks/switch-mode.sh full
```