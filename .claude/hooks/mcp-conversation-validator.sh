#!/bin/bash

# MCP Conversation Flow Validator
# Fixes API errors by ensuring proper tool_use → tool_result pairing

set -euo pipefail

# Configuration
# Find project root and .claude directory
find_project_root() {
    local dir="$(pwd)"
    while [ "$dir" != "/" ]; do
        if [ -d "$dir/.claude" ]; then
            echo "$dir"
            return 0
        fi
        dir=$(dirname "$dir")
    done
    return 1
}

PROJECT_ROOT=$(find_project_root)
if [ -z "$PROJECT_ROOT" ]; then
    echo "ERROR: Could not find project root with .claude directory" >&2
    exit 1
fi

# Worktree-aware state directory detection
get_worktree_state_dir() {
    if [[ -f .git ]] && grep -q "gitdir:" .git 2>/dev/null; then
        # This is a worktree
        local worktree_name=$(basename "$(pwd)")
        if [[ "$worktree_name" == pib-* ]]; then
            local feature_name="${worktree_name#pib-}"
            echo "$PROJECT_ROOT/.claude/state/worktrees/$feature_name"
        else
            echo "$PROJECT_ROOT/.claude/state/main"
        fi
    else
        # This is the main repository
        echo "$PROJECT_ROOT/.claude/state/main"
    fi
}

# Use worktree-aware state directory
STATE_BASE_DIR=$(get_worktree_state_dir)
MCP_STATE_DIR="$STATE_BASE_DIR/mcp-conversations"
CONVERSATION_LOG="$MCP_STATE_DIR/conversation-flow.log"
ERROR_LOG="$MCP_STATE_DIR/error-recovery.log"

# Ensure state directory exists
mkdir -p "$MCP_STATE_DIR"

# Initialize logging
exec 3>> "$CONVERSATION_LOG"
exec 4>> "$ERROR_LOG"

log_conversation() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >&3
}

log_error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1" >&4
}

# Track MCP tool usage state
TOOL_USE_STATE_FILE="$MCP_STATE_DIR/current-tool-state.json"

# Initialize tool state if not exists
initialize_tool_state() {
    if [[ ! -f "$TOOL_USE_STATE_FILE" ]]; then
        cat > "$TOOL_USE_STATE_FILE" << 'EOF'
{
  "active_tool_calls": [],
  "pending_results": [],
  "conversation_id": null,
  "last_update": null
}
EOF
    fi
}

# Register a new tool use
register_tool_use() {
    local tool_id="$1"
    local tool_name="$2"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    log_conversation "Registering tool use: $tool_name ($tool_id)"
    
    # Update state file
    jq --arg id "$tool_id" --arg name "$tool_name" --arg ts "$timestamp" '
        .active_tool_calls += [{
            "tool_id": $id,
            "tool_name": $name,
            "timestamp": $ts,
            "status": "pending"
        }] |
        .last_update = $ts
    ' "$TOOL_USE_STATE_FILE" > "$TOOL_USE_STATE_FILE.tmp" && mv "$TOOL_USE_STATE_FILE.tmp" "$TOOL_USE_STATE_FILE"
}

# Register tool result
register_tool_result() {
    local tool_id="$1"
    local result_status="$2"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    log_conversation "Registering tool result: $tool_id ($result_status)"
    
    # Update state file
    jq --arg id "$tool_id" --arg status "$result_status" --arg ts "$timestamp" '
        .active_tool_calls = [.active_tool_calls[] | 
            if .tool_id == $id then 
                . + {"status": $status, "completed": $ts}
            else 
                . 
            end
        ] |
        .last_update = $ts
    ' "$TOOL_USE_STATE_FILE" > "$TOOL_USE_STATE_FILE.tmp" && mv "$TOOL_USE_STATE_FILE.tmp" "$TOOL_USE_STATE_FILE"
}

# Check for orphaned tool calls
check_orphaned_tools() {
    local orphaned_tools
    orphaned_tools=$(jq -r '.active_tool_calls[] | select(.status == "pending") | .tool_id' "$TOOL_USE_STATE_FILE")
    
    if [[ -n "$orphaned_tools" ]]; then
        log_error "Found orphaned tool calls: $orphaned_tools"
        echo "$orphaned_tools"
        return 1
    fi
    return 0
}

# Generate error tool result for orphaned tools
generate_error_tool_result() {
    local tool_id="$1"
    local error_message="$2"
    
    cat << EOF
{
  "role": "user",
  "content": [
    {
      "type": "tool_result",
      "tool_use_id": "$tool_id",
      "content": "Error: $error_message",
      "is_error": true
    }
  ]
}
EOF
}

# Validate conversation flow before MCP execution
validate_mcp_conversation() {
    local conversation_context="$1"
    
    log_conversation "Validating MCP conversation flow"
    
    # Check for incomplete tool use blocks
    if echo "$conversation_context" | grep -q '"type": "tool_use"'; then
        local tool_id
        tool_id=$(echo "$conversation_context" | jq -r '.content[]? | select(.type == "tool_use") | .id' | tail -1)
        
        if [[ -n "$tool_id" ]]; then
            # Check if this tool has a corresponding result
            if ! echo "$conversation_context" | grep -q "\"tool_use_id\": \"$tool_id\""; then
                log_error "Incomplete tool use found: $tool_id"
                
                # Generate error tool result
                local error_result
                error_result=$(generate_error_tool_result "$tool_id" "Tool execution was interrupted or incomplete")
                
                echo "$error_result"
                return 1
            fi
        fi
    fi
    
    return 0
}

# Cleanup old conversation state
cleanup_conversation_state() {
    local max_age_hours=24
    
    # Remove state files older than max_age_hours
    find "$MCP_STATE_DIR" -name "*.json" -type f -mtime +$((max_age_hours / 24)) -delete 2>/dev/null || true
    
    # Trim log files to last 1000 lines
    if [[ -f "$CONVERSATION_LOG" ]]; then
        tail -1000 "$CONVERSATION_LOG" > "$CONVERSATION_LOG.tmp" && mv "$CONVERSATION_LOG.tmp" "$CONVERSATION_LOG"
    fi
    
    if [[ -f "$ERROR_LOG" ]]; then
        tail -1000 "$ERROR_LOG" > "$ERROR_LOG.tmp" && mv "$ERROR_LOG.tmp" "$ERROR_LOG"
    fi
}

# Recovery mechanisms for broken MCP chains
recover_broken_mcp_chain() {
    local recovery_strategy="$1"
    
    case "$recovery_strategy" in
        "graceful_degradation")
            log_conversation "Implementing graceful degradation - continuing without MCP tools"
            ;;
        "retry_with_timeout")
            log_conversation "Implementing retry with timeout mechanism"
            ;;
        "fallback_to_basic")
            log_conversation "Falling back to basic agent capabilities"
            ;;
        *)
            log_error "Unknown recovery strategy: $recovery_strategy"
            return 1
            ;;
    esac
}

# Main validation function
main() {
    local action="${1:-validate}"
    
    initialize_tool_state
    
    case "$action" in
        "validate")
            if [[ $# -ge 2 ]]; then
                validate_mcp_conversation "$2"
            else
                log_error "No conversation context provided for validation"
                return 1
            fi
            ;;
        "register_tool")
            if [[ $# -ge 3 ]]; then
                register_tool_use "$2" "$3"
            else
                log_error "Missing tool registration parameters"
                return 1
            fi
            ;;
        "register_result")
            if [[ $# -ge 3 ]]; then
                register_tool_result "$2" "$3"
            else
                log_error "Missing result registration parameters"
                return 1
            fi
            ;;
        "check_orphaned")
            check_orphaned_tools
            ;;
        "cleanup")
            cleanup_conversation_state
            ;;
        "recover")
            if [[ $# -ge 2 ]]; then
                recover_broken_mcp_chain "$2"
            else
                recover_broken_mcp_chain "graceful_degradation"
            fi
            ;;
        *)
            echo "Usage: $0 {validate|register_tool|register_result|check_orphaned|cleanup|recover} [args...]"
            exit 1
            ;;
    esac
}

# Execute main function if script is run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi