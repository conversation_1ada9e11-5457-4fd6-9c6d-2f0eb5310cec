#!/bin/bash

# Workflow Manager
# Provides utilities for managing PIB agent workflow states and transitions

# Set error handling
set -o pipefail

# === UTILITY FUNCTIONS ===

# Find .claude directory
find_claude_dir() {
    local dir="$(pwd)"
    while [ "$dir" != "/" ]; do
        if [ -d "$dir/.claude" ]; then
            echo "$dir/.claude"
            return 0
        fi
        dir=$(dirname "$dir")
    done
    return 1
}

# Initialize workflow state if it doesn't exist
init_workflow_state() {
    local claude_dir="$1"
    local workflow_state_file="$claude_dir/current-workflow-state.json"
    
    if [ ! -f "$workflow_state_file" ]; then
        cat > "$workflow_state_file" <<EOF
{
  "workflow": "development",
  "currentStage": "development",
  "currentAgent": "developer",
  "lastUpdate": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "triggeredBy": "initialization",
  "context": {
    "initialized": true
  }
}
EOF
        echo "Workflow state initialized" >&2
    fi
}

# Get current workflow state
get_workflow_state() {
    local claude_dir="$1"
    local workflow_state_file="$claude_dir/current-workflow-state.json"
    
    if [ -f "$workflow_state_file" ]; then
        cat "$workflow_state_file"
    else
        echo '{"workflow":"development","currentStage":"development","currentAgent":"developer"}'
    fi
}

# Update workflow state
update_workflow_state() {
    local claude_dir="$1"
    local stage="$2"
    local agent="$3"
    local trigger="$4"
    local workflow_state_file="$claude_dir/current-workflow-state.json"
    
    cat > "$workflow_state_file" <<EOF
{
  "workflow": "development",
  "currentStage": "$stage",
  "currentAgent": "$agent",
  "lastUpdate": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "triggeredBy": "$trigger",
  "context": {
    "transition": "automated"
  }
}
EOF
}

# Check if workflow transition is needed
should_transition() {
    local current_stage="$1"
    local tool_name="$2"
    
    case "$tool_name" in
        "Write"|"Edit"|"MultiEdit")
            case "$current_stage" in
                "development"|"implementation")
                    echo "review"
                    return 0
                    ;;
            esac
            ;;
    esac
    
    echo "$current_stage"
    return 1
}

# === MAIN WORKFLOW LOGIC ===
main() {
    local command="${1:-status}"
    
    local claude_dir=$(find_claude_dir)
    if [ -z "$claude_dir" ]; then
        echo "Error: .claude directory not found" >&2
        exit 1
    fi
    
    case "$command" in
        "init")
            init_workflow_state "$claude_dir"
            ;;
        "status")
            get_workflow_state "$claude_dir"
            ;;
        "transition")
            local new_stage="$2"
            local new_agent="$3"
            local trigger="$4"
            update_workflow_state "$claude_dir" "$new_stage" "$new_agent" "$trigger"
            ;;
        "check-transition")
            local current_stage="$2"
            local tool_name="$3"
            should_transition "$current_stage" "$tool_name"
            ;;
        *)
            echo "Usage: $0 {init|status|transition|check-transition}" >&2
            exit 1
            ;;
    esac
}

# Execute main function if script is run directly
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi