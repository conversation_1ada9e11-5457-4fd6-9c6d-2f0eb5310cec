#!/bin/bash

# Async wrapper script for hooks
# Runs hooks in background to avoid blocking <PERSON>

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# The actual script to run is passed as first argument
HOOK_SCRIPT="$1"
shift  # Remove first argument, pass rest to the hook

# Check if hook script exists
if [ ! -f "$HOOK_SCRIPT" ]; then
    echo "Hook script not found: $HOOK_SCRIPT"
    exit 1
fi

# Run the hook in background, redirecting output to avoid blocking
{
    "$HOOK_SCRIPT" "$@" > /dev/null 2>&1
} &

# Return immediately
exit 0