#!/bin/bash

# Change Implementer Agent Hook
# Automatically invokes the change implementation agent after code review completion

# Set error handling
set -o pipefail
trap '' PIPE

# === READ AND PASS THROUGH JSON FIRST ===
# Read JSON input from stdin (REQUIRED for all Claude Code hooks)
if ! JSON_INPUT=$(cat 2>/dev/null); then
    # If we can't read stdin, exit gracefully
    exit 0
fi

# === DEPENDENCY VALIDATION ===
if ! command -v jq >/dev/null 2>&1; then
    # Pass through JSON and exit if jq not available
    echo "$JSON_INPUT"
    exit 0
fi

# Find .claude directory
find_claude_dir() {
    local dir="$(pwd)"
    while [ "$dir" != "/" ]; do
        if [ -d "$dir/.claude" ]; then
            echo "$dir/.claude"
            return 0
        fi
        dir=$(dirname "$dir")
    done
    return 1
}

CLAUDE_DIR=$(find_claude_dir)
if [ -z "$CLAUDE_DIR" ]; then
    echo "$JSON_INPUT"
    exit 0
fi

CHANGE_TRIGGER_FILE="$CLAUDE_DIR/change-trigger.json"
ORCHESTRATION_LOG="$CLAUDE_DIR/orchestration.log"

# Check if change implementation is triggered - if not, just pass through JSON
if [ ! -f "$CHANGE_TRIGGER_FILE" ]; then
    echo "$JSON_INPUT"
    exit 0
fi

# Read trigger context
TRIGGER_CONTEXT=$(cat "$CHANGE_TRIGGER_FILE" 2>/dev/null)
if [ -z "$TRIGGER_CONTEXT" ]; then
    exit 0
fi

# Extract context information
PROJECT_NAME=$(echo "$TRIGGER_CONTEXT" | jq -r '.context.projectName // "unknown"' 2>/dev/null)
WORKING_DIR=$(echo "$TRIGGER_CONTEXT" | jq -r '.context.workingDirectory // "."' 2>/dev/null)

# Log the change implementation initiation
echo "$(date): Change implementation triggered for project: $PROJECT_NAME" >> "$ORCHESTRATION_LOG"

# === REVIEW RESULTS ANALYSIS ===
analyze_review_results() {
    local review_results_file="$CLAUDE_DIR/latest-review-results.json"
    local changes_needed=false
    local critical_issues=false
    
    # Check if review results exist
    if [ -f "$review_results_file" ]; then
        local status=$(jq -r '.status // "unknown"' "$review_results_file" 2>/dev/null)
        local critical_count=$(jq -r '.issues[] | select(.severity == "critical") | length' "$review_results_file" 2>/dev/null | wc -l)
        local high_count=$(jq -r '.issues[] | select(.severity == "high") | length' "$review_results_file" 2>/dev/null | wc -l)
        
        if [ "$status" = "changes_required" ] || [ "$critical_count" -gt 0 ] || [ "$high_count" -gt 0 ]; then
            changes_needed=true
        fi
        
        if [ "$critical_count" -gt 0 ]; then
            critical_issues=true
        fi
    else
        # Assume changes needed if no explicit review results
        changes_needed=true
    fi
    
    echo "changes_needed:$changes_needed,critical_issues:$critical_issues"
}

# === CHANGE IMPLEMENTATION SCOPE ===
determine_implementation_scope() {
    local analysis_result=$(analyze_review_results)
    local changes_needed=$(echo "$analysis_result" | cut -d',' -f1 | cut -d':' -f2)
    local critical_issues=$(echo "$analysis_result" | cut -d',' -f2 | cut -d':' -f2)
    
    if [ "$critical_issues" = "true" ]; then
        echo "critical"
    elif [ "$changes_needed" = "true" ]; then
        echo "improvements"
    else
        echo "approved"
    fi
}

# === CHANGE IMPLEMENTER INVOCATION ===
invoke_change_implementer() {
    local implementation_scope=$(determine_implementation_scope)
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    # Create implementation context file
    local implementation_context_file="$CLAUDE_DIR/current-implementation-context.json"
    cat > "$implementation_context_file" <<EOF
{
  "implementationId": "auto-impl-$(date +%s)",
  "timestamp": "$timestamp",
  "trigger": "post_review",
  "scope": "$implementation_scope",
  "projectContext": {
    "name": "$PROJECT_NAME",
    "workingDirectory": "$WORKING_DIR"
  },
  "implementationCriteria": {
    "followLever": true,
    "maintainQuality": true,
    "addressReviewFindings": true,
    "preserveWorkingCode": true
  }
}
EOF
    
    # Log the implementation details
    echo "$(date): Initiating change implementation with scope: $implementation_scope" >> "$ORCHESTRATION_LOG"
    
    # Create implementation instruction based on scope
    local implementation_instruction_file="$CLAUDE_DIR/pending-implementation-instruction.md"
    
    case "$implementation_scope" in
        "critical")
            cat > "$implementation_instruction_file" <<EOF
# CRITICAL: Immediate Implementation Required

## Context
- **Project**: $PROJECT_NAME
- **Scope**: Critical issues found in code review
- **Timestamp**: $timestamp

## Priority: CRITICAL ISSUES MUST BE ADDRESSED

### Review Findings
Critical security, functionality, or compliance issues were identified that require immediate attention.

### Implementation Instructions
1. **Immediate Action Required**: Address all critical issues identified in the code review
2. **Use the change implementer agent persona**
3. **Follow LEVER framework principles**
4. **Ensure no breaking changes unless absolutely necessary**

### Commands to Execute
\`\`\`bash
# Switch to change implementer persona
*persona change-implementer

# Review the critical findings
*analyze .claude/latest-review-results.json

# Implement critical fixes
*refactor --scope=critical --preserve-functionality
\`\`\`

### Verification Required
- [ ] All critical issues addressed
- [ ] No new bugs introduced
- [ ] Code still passes existing tests
- [ ] LEVER principles maintained

---
*CRITICAL PRIORITY - Generated by PIB Agent Orchestration System*
EOF
            ;;
        "improvements")
            cat > "$implementation_instruction_file" <<EOF
# Code Improvements Implementation

## Context
- **Project**: $PROJECT_NAME
- **Scope**: Quality improvements and optimizations
- **Timestamp**: $timestamp

## Review Findings Summary
The code review identified areas for improvement that enhance quality, maintainability, or performance.

### Implementation Instructions
1. **Address review findings systematically**
2. **Use the change implementer agent persona**
3. **Apply LEVER framework principles**
4. **Prioritize high-impact improvements**

### Commands to Execute
\`\`\`bash
# Switch to change implementer persona
*persona change-implementer

# Review all findings
*analyze .claude/latest-review-results.json

# Implement improvements
*refactor --scope=improvements --maintain-functionality
\`\`\`

### Implementation Priorities
1. High-severity issues first
2. LEVER compliance improvements
3. Code quality enhancements
4. Performance optimizations
5. Documentation updates

### Verification Steps
- [ ] All high-priority issues addressed
- [ ] Code quality metrics improved
- [ ] No functionality regression
- [ ] Documentation updated

---
*Generated by PIB Agent Orchestration System*
EOF
            ;;
        "approved")
            cat > "$implementation_instruction_file" <<EOF
# Code Review Approved - No Changes Required

## Context
- **Project**: $PROJECT_NAME
- **Scope**: Code review completed successfully
- **Timestamp**: $timestamp

## Review Outcome
✅ **Code Review Passed**: No critical issues or required changes identified.

### Optional Enhancements
While no changes are required, consider these optional improvements:

\`\`\`bash
# Optional: Look for micro-optimizations
*analyze --scope=optimization

# Optional: Check for documentation improvements
*docgen --scope=enhancement
\`\`\`

### Next Steps
- [ ] Merge approved changes
- [ ] Update documentation if needed
- [ ] Continue with development workflow

---
*SUCCESS - Generated by PIB Agent Orchestration System*
EOF
            ;;
    esac
    
    # Create a notification for the user
    echo "$(date): Implementation instruction created at: $implementation_instruction_file" >> "$ORCHESTRATION_LOG"
    
    return 0
}

# === WORKFLOW TRANSITION ===
complete_implementation_workflow() {
    local workflow_state_file="$CLAUDE_DIR/current-workflow-state.json"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    # Update workflow state back to development
    cat > "$workflow_state_file" <<EOF
{
  "workflow": "development",
  "currentStage": "development",
  "currentAgent": "developer",
  "lastUpdate": "$timestamp",
  "triggeredBy": "change-implementer-completion",
  "context": {
    "lastAction": "implementation_complete",
    "cycleDuration": "$(date +%s)"
  }
}
EOF
    
    echo "$(date): Workflow cycle completed - returning to development stage" >> "$ORCHESTRATION_LOG"
}

# === MAIN EXECUTION ===
# Invoke the change implementation process
invoke_change_implementer

# Complete the workflow cycle
complete_implementation_workflow

# Clean up the trigger file
rm -f "$CHANGE_TRIGGER_FILE"

# Mark implementation as initiated
echo "$(date): Change implementer agent completed initialization" >> "$ORCHESTRATION_LOG"

# CRITICAL: Always pass through the JSON for the next hook/tool
echo "$JSON_INPUT"

exit 0