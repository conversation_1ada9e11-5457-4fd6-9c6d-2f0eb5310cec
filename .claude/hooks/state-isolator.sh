#!/bin/bash

# State Isolation Manager for PIB Worktrees
# Manages isolated state directories for each worktree to prevent cross-contamination

set -euo pipefail

# Configuration
# Find project root and .claude directory
find_project_root() {
    local dir="$(pwd)"
    while [ "$dir" != "/" ]; do
        if [ -d "$dir/.claude" ]; then
            echo "$dir"
            return 0
        fi
        dir=$(dirname "$dir")
    done
    return 1
}

PROJECT_ROOT=$(find_project_root)
if [ -z "$PROJECT_ROOT" ]; then
    echo "ERROR: Could not find project root with .claude directory" >&2
    exit 1
fi

MAIN_STATE_DIR="$PROJECT_ROOT/.claude/state/main"
WORKTREE_STATES_DIR="$PROJECT_ROOT/.claude/state/worktrees"
SHARED_STATE_DIR="$PROJECT_ROOT/.claude/state/shared"
STATE_TEMPLATES_DIR="$PROJECT_ROOT/.claude/state/templates"

# Ensure required directories exist
mkdir -p "$MAIN_STATE_DIR" "$WORKTREE_STATES_DIR" "$SHARED_STATE_DIR" "$STATE_TEMPLATES_DIR"

# Logging
log_state_operation() {
    local log_file="$PROJECT_ROOT/.claude/state/state-operations.log"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "$log_file"
}

# Detect current worktree context and return appropriate state directory
get_current_state_dir() {
    if [[ -f .git ]] && grep -q "gitdir:" .git 2>/dev/null; then
        # This is a worktree
        local worktree_name=$(basename "$(pwd)")
        if [[ "$worktree_name" == pib-* ]]; then
            local feature_name="${worktree_name#pib-}"
            echo "$WORKTREE_STATES_DIR/$feature_name"
        else
            echo "$MAIN_STATE_DIR"
        fi
    else
        # This is the main repository
        echo "$MAIN_STATE_DIR"
    fi
}

# Initialize state templates for new worktrees
initialize_state_templates() {
    log_state_operation "Initializing state templates"
    
    # Create template for context-engine state
    local context_template="$STATE_TEMPLATES_DIR/context-engine-template.json"
    if [[ ! -f "$context_template" ]]; then
        cat > "$context_template" << 'EOF'
{
  "task_patterns": {},
  "context_weights": {
    "immediate": 1.0,
    "session": 0.8,
    "project": 0.6,
    "domain": 0.4
  },
  "last_updated": null
}
EOF
    fi
    
    # Create template for tool selection cache
    local tool_cache_template="$STATE_TEMPLATES_DIR/tool-selection-cache-template.json"
    if [[ ! -f "$tool_cache_template" ]]; then
        cat > "$tool_cache_template" << 'EOF'
{
  "cached_selections": {},
  "usage_patterns": {},
  "effectiveness_scores": {},
  "last_updated": null
}
EOF
    fi
    
    # Create template for MCP conversation state
    local mcp_template="$STATE_TEMPLATES_DIR/mcp-conversations-template.json"
    if [[ ! -f "$mcp_template" ]]; then
        cat > "$mcp_template" << 'EOF'
{
  "active_tool_calls": [],
  "pending_results": [],
  "conversation_id": null,
  "last_update": null
}
EOF
    fi
    
    # Create template for workflow context evolution
    local evolution_template="$STATE_TEMPLATES_DIR/context-evolution-template.json"
    if [[ ! -f "$evolution_template" ]]; then
        cat > "$evolution_template" << 'EOF'
{
  "evolution_patterns": {},
  "successful_transitions": [],
  "context_quality_metrics": {},
  "last_updated": null
}
EOF
    fi
}

# Initialize isolated state for a new worktree
initialize_worktree_state() {
    local feature_name="$1"
    local worktree_dir="$2"
    
    local state_dir="$WORKTREE_STATES_DIR/$feature_name"
    
    log_state_operation "Initializing worktree state: $feature_name -> $state_dir"
    
    # Create state directory structure
    mkdir -p "$state_dir"
    mkdir -p "$state_dir/context-engine"
    mkdir -p "$state_dir/context-evolution"
    mkdir -p "$state_dir/mcp-conversations"
    mkdir -p "$state_dir/tool-selection"
    mkdir -p "$state_dir/workflow-contexts"
    
    # Copy templates to new state directory
    if [[ -d "$STATE_TEMPLATES_DIR" ]]; then
        for template in "$STATE_TEMPLATES_DIR"/*-template.json; do
            if [[ -f "$template" ]]; then
                local target_name=$(basename "$template" | sed 's/-template//')
                local target_subdir
                
                case "$target_name" in
                    "context-engine"*)
                        target_subdir="context-engine"
                        ;;
                    "tool-selection"*)
                        target_subdir="tool-selection"
                        ;;
                    "mcp-conversations"*)
                        target_subdir="mcp-conversations"
                        ;;
                    "context-evolution"*)
                        target_subdir="context-evolution"
                        ;;
                    *)
                        target_subdir="."
                        ;;
                esac
                
                cp "$template" "$state_dir/$target_subdir/$target_name"
            fi
        done
    fi
    
    # Copy essential shared configuration (read-only data)
    if [[ -d "$SHARED_STATE_DIR" ]]; then
        for shared_file in "$SHARED_STATE_DIR"/*; do
            if [[ -f "$shared_file" ]]; then
                local filename=$(basename "$shared_file")
                if [[ "$filename" == *.readonly.* ]] || [[ "$filename" == config-* ]]; then
                    cp "$shared_file" "$state_dir/"
                fi
            fi
        done
    fi
    
    # Copy relevant data from main state (if available)
    if [[ -d "$MAIN_STATE_DIR" ]]; then
        # Copy project-wide context that should be shared
        local context_files=(
            "project-context.md"
            "architecture-overview.md"
            "coding-standards.md"
        )
        
        for context_file in "${context_files[@]}"; do
            if [[ -f "$MAIN_STATE_DIR/$context_file" ]]; then
                cp "$MAIN_STATE_DIR/$context_file" "$state_dir/"
            fi
        done
    fi
    
    # Create worktree-specific metadata
    cat > "$state_dir/worktree-metadata.json" << EOF
{
  "feature_name": "$feature_name",
  "worktree_directory": "$worktree_dir",
  "created": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "state_directory": "$state_dir",
  "isolation_level": "full",
  "parent_state": "$MAIN_STATE_DIR"
}
EOF
    
    log_state_operation "Worktree state initialized successfully: $feature_name"
    
    echo "✅ Isolated state initialized for worktree: $feature_name"
    echo "   State directory: $state_dir"
    echo "   Isolation: Full (no cross-contamination with other worktrees)"
}

# Sync shared data to all worktrees
sync_shared_data() {
    local force="${1:-false}"
    
    log_state_operation "Syncing shared data to all worktrees"
    
    if [[ ! -d "$SHARED_STATE_DIR" ]]; then
        echo "No shared state directory found. Creating..."
        mkdir -p "$SHARED_STATE_DIR"
    fi
    
    # Find all active worktree state directories
    local synced_count=0
    
    for worktree_state in "$WORKTREE_STATES_DIR"/*; do
        if [[ -d "$worktree_state" ]]; then
            local feature_name=$(basename "$worktree_state")
            
            echo "Syncing shared data to: $feature_name"
            
            # Copy shared configuration files
            for shared_file in "$SHARED_STATE_DIR"/*; do
                if [[ -f "$shared_file" ]]; then
                    local filename=$(basename "$shared_file")
                    local target="$worktree_state/$filename"
                    
                    # Only sync if file doesn't exist or force is true
                    if [[ ! -f "$target" ]] || [[ "$force" == "true" ]]; then
                        cp "$shared_file" "$target"
                        echo "  ↳ Synced: $filename"
                    fi
                fi
            done
            
            ((synced_count++))
        fi
    done
    
    log_state_operation "Shared data sync completed: $synced_count worktrees updated"
    echo "✅ Shared data synced to $synced_count worktrees"
}

# Backup state before potentially destructive operations
backup_state() {
    local state_dir="$1"
    local backup_reason="${2:-manual}"
    
    if [[ ! -d "$state_dir" ]]; then
        echo "ERROR: State directory not found: $state_dir" >&2
        return 1
    fi
    
    local backup_base="$PROJECT_ROOT/.claude/state/backups"
    mkdir -p "$backup_base"
    
    local timestamp=$(date +%Y%m%d-%H%M%S)
    local state_name=$(basename "$state_dir")
    local backup_dir="$backup_base/${state_name}-${backup_reason}-${timestamp}"
    
    log_state_operation "Creating state backup: $state_dir -> $backup_dir"
    
    cp -r "$state_dir" "$backup_dir"
    
    # Create backup metadata
    cat > "$backup_dir/backup-metadata.json" << EOF
{
  "original_path": "$state_dir",
  "backup_reason": "$backup_reason",
  "created": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "backup_directory": "$backup_dir"
}
EOF
    
    echo "✅ State backup created: $backup_dir"
    echo "$backup_dir"
}

# Restore state from backup
restore_state() {
    local backup_dir="$1"
    local target_dir="${2:-auto}"
    
    if [[ ! -d "$backup_dir" ]]; then
        echo "ERROR: Backup directory not found: $backup_dir" >&2
        return 1
    fi
    
    # Determine target directory
    if [[ "$target_dir" == "auto" ]]; then
        if [[ -f "$backup_dir/backup-metadata.json" ]]; then
            target_dir=$(jq -r '.original_path' "$backup_dir/backup-metadata.json" 2>/dev/null || echo "")
        fi
        
        if [[ -z "$target_dir" ]]; then
            echo "ERROR: Cannot determine target directory for restore" >&2
            return 1
        fi
    fi
    
    log_state_operation "Restoring state: $backup_dir -> $target_dir"
    
    # Backup current state before restore
    if [[ -d "$target_dir" ]]; then
        backup_state "$target_dir" "pre-restore"
    fi
    
    # Restore from backup
    rm -rf "$target_dir"
    cp -r "$backup_dir" "$target_dir"
    
    # Remove backup metadata from restored directory
    rm -f "$target_dir/backup-metadata.json"
    
    log_state_operation "State restore completed: $target_dir"
    echo "✅ State restored from backup: $target_dir"
}

# Cleanup old backups
cleanup_old_backups() {
    local days="${1:-30}"
    local backup_dir="$PROJECT_ROOT/.claude/state/backups"
    
    if [[ ! -d "$backup_dir" ]]; then
        echo "No backup directory found."
        return 0
    fi
    
    log_state_operation "Cleaning up backups older than $days days"
    
    local cleaned_count=0
    
    find "$backup_dir" -type d -name "*-*-*" -mtime +$days | while read -r old_backup; do
        if [[ -d "$old_backup" ]]; then
            echo "Removing old backup: $(basename "$old_backup")"
            rm -rf "$old_backup"
            ((cleaned_count++))
        fi
    done
    
    echo "✅ Cleaned up old backups (older than $days days)"
}

# Validate state directory integrity
validate_state() {
    local state_dir="$1"
    
    if [[ ! -d "$state_dir" ]]; then
        echo "❌ State directory not found: $state_dir"
        return 1
    fi
    
    local issues=0
    
    echo "🔍 Validating state directory: $state_dir"
    
    # Check required subdirectories
    local required_dirs=(
        "context-engine"
        "context-evolution"
        "mcp-conversations"
        "tool-selection"
        "workflow-contexts"
    )
    
    for req_dir in "${required_dirs[@]}"; do
        if [[ ! -d "$state_dir/$req_dir" ]]; then
            echo "❌ Missing required directory: $req_dir"
            ((issues++))
        else
            echo "✅ Directory exists: $req_dir"
        fi
    done
    
    # Check JSON file validity
    for json_file in "$state_dir"/*.json "$state_dir"/*/*.json; do
        if [[ -f "$json_file" ]]; then
            if ! jq . "$json_file" >/dev/null 2>&1; then
                echo "❌ Invalid JSON file: $json_file"
                ((issues++))
            fi
        fi
    done
    
    if [[ $issues -eq 0 ]]; then
        echo "✅ State validation passed: No issues found"
        return 0
    else
        echo "❌ State validation failed: $issues issues found"
        return 1
    fi
}

# Main function
main() {
    local action="${1:-help}"
    
    # Initialize templates on first run
    initialize_state_templates
    
    case "$action" in
        "initialize")
            if [[ $# -ge 3 ]]; then
                initialize_worktree_state "$2" "$3"
            else
                echo "Usage: $0 initialize <feature_name> <worktree_dir>"
                exit 1
            fi
            ;;
        "get-current-dir")
            get_current_state_dir
            ;;
        "sync-shared")
            sync_shared_data "${2:-false}"
            ;;
        "backup")
            if [[ $# -ge 2 ]]; then
                backup_state "$2" "${3:-manual}"
            else
                echo "Usage: $0 backup <state_dir> [reason]"
                exit 1
            fi
            ;;
        "restore")
            if [[ $# -ge 2 ]]; then
                restore_state "$2" "${3:-auto}"
            else
                echo "Usage: $0 restore <backup_dir> [target_dir]"
                exit 1
            fi
            ;;
        "cleanup-backups")
            cleanup_old_backups "${2:-30}"
            ;;
        "validate")
            if [[ $# -ge 2 ]]; then
                validate_state "$2"
            else
                # Validate current state
                local current_state=$(get_current_state_dir)
                validate_state "$current_state"
            fi
            ;;
        "help"|*)
            cat << EOF
State Isolation Manager for PIB Worktrees

Usage: $0 <action> [arguments...]

Actions:
  initialize <feature_name> <worktree_dir>
    Initialize isolated state for a new worktree
    
  get-current-dir
    Get the current state directory path
    
  sync-shared [force]
    Sync shared data to all worktree states
    
  backup <state_dir> [reason]
    Create a backup of the specified state directory
    
  restore <backup_dir> [target_dir]
    Restore state from a backup directory
    
  cleanup-backups [days]
    Remove backups older than specified days (default: 30)
    
  validate [state_dir]
    Validate state directory integrity
    
  help
    Show this help message

Examples:
  $0 initialize user-auth /path/to/worktree
  $0 get-current-dir
  $0 sync-shared force
  $0 validate
  $0 backup /path/to/state pre-merge
  $0 cleanup-backups 60

State Directory Structure:
  main/                 # Main repository state
  worktrees/           # Individual worktree states
    feature-name/      # Isolated state per feature
  shared/              # Shared configuration data
  templates/           # State templates for new worktrees
  backups/             # State backups for recovery
EOF
            ;;
    esac
}

# Execute main function if script is run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi