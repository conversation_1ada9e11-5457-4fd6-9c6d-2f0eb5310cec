#!/bin/bash

# PIB Mode Switcher
# Switch between development mode (fast) and full mode (all hooks)

MODE="$1"

if [ -z "$MODE" ]; then
    echo "Usage: .claude/switch-mode.sh [dev|full]"
    echo ""
    echo "Current mode:"
    if [ -f ".claude/settings.local.json" ] && grep -q "workflow-status-display" ".claude/settings.local.json"; then
        echo "  🔧 FULL MODE (all hooks active)"
    else
        echo "  ⚡ DEV MODE (minimal hooks for speed)"
    fi
    echo ""
    echo "Modes:"
    echo "  dev  - Fast development mode (no lint, no workflow display)"
    echo "  full - Full mode with all hooks (workflow tracking, lint, etc.)"
    exit 0
fi

case "$MODE" in
    "dev"|"development")
        echo "⚡ Switching to DEVELOPMENT mode..."
        cp ".claude/settings.dev.json" ".claude/settings.local.json"
        echo "✅ DEV mode active - fast coding, no automatic lint"
        echo "💡 Run manual lint: .claude/hooks/manual-lint.sh"
        ;;
    "full"|"production"|"normal")
        echo "🔧 Switching to FULL mode..."
        cp ".claude/settings.local.json.backup" ".claude/settings.local.json" 2>/dev/null || {
            echo "❌ No backup found. Creating full mode from dev mode..."
            # Recreate full mode by adding back the missing hooks
            # This would need the full settings, but for now just copy from source
            echo "⚠️  Please run sync to restore full mode, or manually switch back"
        }
        echo "✅ FULL mode active - all hooks, workflow tracking, automatic lint"
        ;;
    *)
        echo "❌ Unknown mode: $MODE"
        echo "Use: dev or full"
        exit 1
        ;;
esac

echo ""
echo "🔄 Restart Claude Code to apply changes"