---
description: Comprehensive documentation generation following BMAD documentation principles
---

# BMAD Documentation Generation Command

**Usage**: `*docgen [scope] [doc-type]`

## Purpose

Generate comprehensive documentation for code, APIs, and systems following BMAD documentation principles and agent-specific expertise for maximum clarity and maintainability.

## LEVER Framework Documentation Analysis

Before documentation generation, establish documentation principles:
- **L** - Leverage: What existing documentation patterns and tools can we build upon?
- **E** - Extend: How can we enhance existing documentation rather than recreating?
- **V** - Verify: How can we validate that documentation accurately reflects the code?
- **E** - Eliminate: What redundant or outdated documentation can we remove?
- **R** - Reduce: What's the simplest documentation approach that provides maximum value?

## Agent-Specific Documentation Focus

### Current Agent Context
Adapt documentation generation based on active agent persona:

#### If Developer Agent Active
- Focus on code-level documentation and inline comments
- Generate API documentation and usage examples
- Create technical implementation details and patterns
- Develop troubleshooting and debugging guides

#### If Architect Agent Active
- Focus on system architecture and design documentation
- Generate component interaction diagrams and flows
- Create architectural decision records (ADRs)
- Develop integration and deployment documentation

#### If Product Agent Active
- Focus on user-facing documentation and guides
- Generate feature specifications and requirements
- Create user journey and workflow documentation
- Develop product roadmap and release notes

#### If DevOps Agent Active
- Focus on operational and deployment documentation
- Generate infrastructure and configuration guides
- Create monitoring and alerting documentation
- Develop CI/CD and automation documentation

## Documentation Categories

### 1. Code Documentation

#### Function and Method Documentation
```bash
# Analyze functions requiring documentation
grep -r "^def\|^function\|^public.*(" --include="*.py" --include="*.js" --include="*.java" . | \
  grep -v "test" | head -20

# Check existing documentation coverage
grep -r "\"\"\"\\|//\\*\\|#.*:" --include="*.py" --include="*.js" --include="*.java" . | wc -l
```

- **Function Signatures**: Parameter types, return values, exceptions
- **Purpose Description**: What the function does and why it exists
- **Usage Examples**: Common use cases and code samples
- **Side Effects**: State changes and external dependencies
- **Complexity Analysis**: Time and space complexity annotations

#### Class and Module Documentation
- **Class Purpose**: High-level responsibility and role
- **Interface Documentation**: Public methods and properties
- **Inheritance Hierarchy**: Parent classes and relationships
- **Design Patterns**: Patterns implemented and design decisions
- **Usage Guidelines**: Best practices and common pitfalls

### 2. API Documentation

#### REST API Documentation
```bash
# Analyze API endpoints
grep -r "app\\.\\(get\\|post\\|put\\|delete\\)" --include="*.py" --include="*.js" . | head -10
grep -r "@\\(GET\\|POST\\|PUT\\|DELETE\\)" --include="*.java" . | head -10
```

- **Endpoint Specifications**: URL patterns, HTTP methods, parameters
- **Request/Response Schemas**: Data structures and validation rules
- **Authentication Requirements**: Security and authorization details
- **Error Handling**: Status codes and error response formats
- **Rate Limiting**: Usage limits and throttling policies

#### GraphQL Documentation
- **Schema Definitions**: Types, queries, mutations, subscriptions
- **Field Documentation**: Purpose and usage of each field
- **Resolver Documentation**: Implementation details and data sources
- **Performance Considerations**: Query complexity and optimization

### 3. Architecture Documentation

#### System Overview
```bash
# Analyze system structure
find . -type d -maxdepth 3 | head -20
find . -name "*.md" -o -name "*.txt" -o -name "README*" | head -10
```

- **System Architecture**: High-level components and relationships
- **Data Flow Diagrams**: Information flow through the system
- **Integration Points**: External services and dependencies
- **Security Architecture**: Authentication, authorization, and data protection
- **Scalability Considerations**: Performance and capacity planning

#### Component Documentation
- **Component Responsibilities**: Single responsibility and boundaries
- **Interface Contracts**: APIs and communication protocols
- **Configuration Options**: Environment variables and settings
- **Dependencies**: Internal and external dependencies
- **Deployment Requirements**: Infrastructure and runtime needs

### 4. User Documentation

#### Installation and Setup
- **Prerequisites**: System requirements and dependencies
- **Installation Steps**: Step-by-step setup instructions
- **Configuration Guide**: Environment setup and customization
- **Verification Steps**: Testing installation and functionality
- **Troubleshooting**: Common issues and solutions

#### Usage Documentation
- **Getting Started**: Quick start guides and tutorials
- **Feature Documentation**: Detailed feature explanations
- **Workflow Guides**: End-to-end process documentation
- **Best Practices**: Recommended usage patterns
- **FAQ**: Frequently asked questions and answers

### 5. LEVER Documentation Validation

#### Leverage Documentation Analysis
- Identify existing documentation patterns and styles
- Assess current documentation tools and frameworks
- Leverage established documentation templates
- Utilize existing technical writing standards

#### Extension Documentation Assessment
- Enhance existing documentation rather than replacing
- Build upon current documentation structure
- Extend existing style guides and templates
- Improve current documentation processes

#### Verification Documentation Methods
- Validate documentation accuracy through code analysis
- Verify documentation completeness through coverage reports
- Test documentation through user feedback
- Confirm documentation maintainability through automation

#### Elimination Documentation Priorities
- Remove outdated and incorrect documentation
- Eliminate redundant and duplicate content
- Remove overly complex documentation structures
- Eliminate unmaintained documentation artifacts

#### Reduction Documentation Strategies
- Simplify documentation structure and navigation
- Reduce cognitive load through clear organization
- Minimize maintenance burden through automation
- Focus on high-value documentation content

## Documentation Generation Strategies

### 1. Automated Code Documentation
```python
# Example docstring generation
def generate_docstring(function_info):
    """
    Generate comprehensive docstring for a function.
    
    Args:
        function_info (dict): Function metadata including name, parameters, 
                            return type, and complexity analysis
    
    Returns:
        str: Formatted docstring following project conventions
        
    Raises:
        ValueError: If function_info is incomplete or invalid
        
    Example:
        >>> func_info = {"name": "calculate", "params": ["x", "y"]}
        >>> docstring = generate_docstring(func_info)
        >>> print(docstring)
        
    Note:
        This function follows Google-style docstring conventions
        and includes complexity analysis when available.
    """
```

### 2. API Documentation Generation
```yaml
# Example OpenAPI specification generation
openapi: 3.0.0
info:
  title: Generated API Documentation
  version: 1.0.0
paths:
  /api/users:
    get:
      summary: Retrieve users
      description: Get a list of users with optional filtering
      parameters:
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/User'
```

### 3. Architecture Documentation Generation
```mermaid
# Example architecture diagram generation
graph TB
    A[Client] --> B[API Gateway]
    B --> C[Authentication Service]
    B --> D[Business Logic Service]
    D --> E[Database]
    D --> F[External API]
    
    subgraph "Security Layer"
        C
    end
    
    subgraph "Data Layer"
        E
        F
    end
```

## Documentation Quality Standards

### 1. Clarity and Readability
- Clear, concise language appropriate for target audience
- Logical organization and information hierarchy
- Consistent terminology and naming conventions
- Visual aids and examples where appropriate

### 2. Accuracy and Completeness
- Documentation reflects current code implementation
- All public APIs and interfaces documented
- Edge cases and error conditions covered
- Performance characteristics and limitations noted

### 3. Maintainability
- Documentation co-located with relevant code
- Automated generation where possible
- Version control integration
- Regular review and update processes

### 4. Accessibility
- Multiple formats for different use cases
- Searchable and navigable structure
- Mobile-friendly presentation
- Internationalization support where needed

## Output Format

### Documentation Summary
- Overall documentation coverage analysis
- Agent perspective and expertise applied
- Priority-ranked documentation opportunities
- LEVER compliance evaluation

### Generated Documentation
- Complete documentation with proper structure
- Framework-appropriate formats and conventions
- Cross-references and navigation aids
- Version control and maintenance metadata

### Style and Standards Guide
- Documentation conventions and patterns
- Writing style and tone guidelines
- Template and format specifications
- Review and approval processes

### Architecture Documentation
- System overview and component relationships
- Data flow and integration documentation
- Security and compliance considerations
- Deployment and operational guides

### Implementation Plan
- Step-by-step documentation creation roadmap
- Review and validation procedures
- Maintenance and update strategies
- Tool integration and automation setup

### Workflow Integration
- Update workflow state with documentation progress
- Document documentation decisions in knowledge base
- Create documentation task tracking and coverage monitoring
- Schedule documentation reviews and quality assessments

## Integration with BMAD System

### Workflow Hooks
- Triggers `read-workflow.sh` for LEVER principles reminder
- Updates workflow state through `workflow-transition.sh`
- Logs documentation generation through `notification-hook.sh`

### Knowledge Management
- Saves documentation to `.ai/documentation/docs-[timestamp].md`
- Updates documentation coverage metrics and quality tracking
- Creates documentation task lists and progress monitoring
- Updates documentation standards and style guides

### Follow-up Commands
Documentation generation prepares context for:
- `*codereview` - Documentation quality review
- `*analyze` - Documentation coverage analysis
- `*testgen` - Documentation example validation
- `*thinkdeep` - Strategic documentation planning

## Example Usage

```bash
# Generate API documentation
*docgen src/api/ api

# Create comprehensive code documentation
*docgen src/ code

# Generate architecture documentation
*docgen . architecture

# Create user documentation
*docgen . user
```

## Quality Gates

All documentation generation must meet BMAD standards:
- **LEVER Compliance**: Demonstrates leverage, extension, verification, elimination, reduction
- **Clarity Focus**: Provides clear, actionable documentation for target audience
- **Agent Alignment**: Respects current agent documentation expertise and perspective
- **Accuracy Requirement**: Reflects current code implementation and behavior
- **Maintainable Output**: Creates documentation that is easy to update and maintain
