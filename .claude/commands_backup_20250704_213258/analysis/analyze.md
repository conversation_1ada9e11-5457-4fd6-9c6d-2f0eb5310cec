---
description: Comprehensive code analysis following BMAD principles and LEVER framework
---

# BMAD Code Analysis Command

**Usage**: `*analyze [files/directories] [focus-area]`

## Purpose

Perform comprehensive code analysis using BMAD principles, LEVER framework validation, and context-aware insights based on current agent persona.

## LEVER Framework Pre-Analysis

Before performing analysis, validate these principles:
- **L** - Leverage: What existing analysis patterns or documentation can we build upon?
- **E** - Extend: Can we extend existing insights rather than starting fresh?
- **V** - Verify: How can we verify findings through existing code patterns?
- **E** - Eliminate: Are we duplicating existing analysis or documentation?
- **R** - Reduce: What's the simplest analysis that provides maximum value?

## Agent-Specific Analysis Focus

### Current Agent Context
Load current agent persona from workflow state and adapt analysis accordingly:

#### If Architect Agent Active
- Focus on system design implications
- Analyze architectural patterns and decisions
- Assess scalability and maintainability concerns
- Evaluate integration points and dependencies

#### If Developer Agent Active  
- Focus on implementation quality and patterns
- Analyze code structure and organization
- Identify refactoring opportunities
- Assess technical debt and complexity

#### If QA Agent Active
- Focus on testability and quality metrics
- Analyze error handling and edge cases
- Identify potential testing gaps
- Assess reliability and robustness

#### If Analyst Agent Active
- Focus on business logic alignment
- Analyze data flow and processing
- Identify optimization opportunities
- Assess performance implications

## Analysis Process

### 1. Context Gathering
```bash
# Determine current workflow state
CURRENT_AGENT=$(cat .claude/current-workflow-state.json | jq -r '.currentAgent // "general"')
PROJECT_CONTEXT=$(cat .ai/project-context.md 2>/dev/null || echo "No project context available")
```

### 2. LEVER Compliance Check
- Search for similar functionality in existing codebase
- Identify extension opportunities vs new implementations
- Validate against existing patterns and standards
- Document compliance with BMAD principles

### 3. Multi-Perspective Analysis
Perform analysis from current agent's perspective while considering:
- **Code Quality**: Structure, readability, maintainability
- **Architecture**: Design patterns, dependencies, modularity  
- **Performance**: Efficiency, scalability, resource usage
- **Security**: Vulnerabilities, best practices, compliance
- **Testing**: Coverage, testability, edge cases

### 4. BMAD Integration Assessment
- How does this code align with BMAD operational guidelines?
- What existing BMAD patterns can be leveraged?
- How can this be extended rather than replaced?
- What complexity can be reduced while maintaining functionality?

## Output Format

### Executive Summary
- Current agent perspective and focus
- LEVER compliance assessment
- Key findings and recommendations
- Integration opportunities with existing BMAD patterns

### Detailed Analysis
- Code structure and organization assessment
- Design pattern identification and evaluation
- Performance and scalability considerations
- Security and reliability concerns
- Testing and quality assurance opportunities

### Actionable Recommendations
- Specific improvements prioritized by impact
- LEVER-compliant enhancement suggestions
- Integration opportunities with existing codebase
- Next steps aligned with BMAD workflows

### Workflow Integration
- Update workflow state with analysis insights
- Trigger appropriate workflow transitions
- Document findings in `.ai/` knowledge base
- Prepare context for follow-up commands

## Integration with BMAD System

### Workflow Hooks
- Triggers `read-workflow.sh` for LEVER principles reminder
- Updates workflow state through `workflow-transition.sh`
- Logs analysis insights through `notification-hook.sh`

### Knowledge Management
- Saves analysis results to `.ai/analysis-[timestamp].md`
- Updates project context with new insights
- Creates issue tracking in `.ai/issues/` if needed

### Follow-up Commands
Analysis results prepare context for:
- `*debug` - Investigation of identified issues
- `*codereview` - Detailed code quality assessment  
- `*refactor` - Improvement implementation
- `*testgen` - Test coverage for identified concerns

## Example Usage

```bash
# Analyze specific files with current agent context
*analyze src/components/UserAuth.tsx src/utils/validation.ts

# Analyze directory focusing on security
*analyze src/auth/ security

# Analyze entire project from architecture perspective  
*analyze . architecture
```

## Quality Gates

All analysis must meet BMAD standards:
- **LEVER Compliance**: Demonstrates leverage, extension, verification, elimination, reduction
- **Agent Alignment**: Respects current agent persona and expertise
- **Workflow Integration**: Properly integrates with BMAD workflow states
- **Actionable Output**: Provides specific, implementable recommendations
- **Knowledge Capture**: Documents insights for future reference
