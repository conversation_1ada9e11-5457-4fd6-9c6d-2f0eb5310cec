#!/bin/bash

# Universal Hook Finder and Runner
# Dynamically finds .claude directory and runs hooks from any subdirectory
# This solves the relative path issue in settings.local.json

# Set error handling
set -o pipefail
trap '' PIPE

# Function to find .claude directory starting from current directory
find_claude_dir() {
    local current_dir="$PWD"
    
    # Walk up the directory tree to find .claude
    while [ "$current_dir" != "/" ]; do
        if [ -d "$current_dir/.claude" ]; then
            echo "$current_dir/.claude"
            return 0
        fi
        current_dir=$(dirname "$current_dir")
    done
    
    # If not found, return error
    return 1
}

# Find the .claude directory
CLAUDE_DIR=$(find_claude_dir)
if [ -z "$CLAUDE_DIR" ]; then
    echo "Error: Could not find .claude directory from $(pwd)" >&2
    exit 2  # Blocking error
fi

# The hook to run is the first argument
HOOK_NAME="$1"
if [ -z "$HOOK_NAME" ]; then
    echo "Error: No hook name provided" >&2
    echo "Usage: find-and-run-hook.sh <hook-name> [args...]" >&2
    exit 2
fi

shift  # Remove hook name, pass remaining args to the hook

# Check if hook exists
HOOK_PATH="$CLAUDE_DIR/hooks/$HOOK_NAME"
if [ ! -f "$HOOK_PATH" ]; then
    echo "Error: Hook not found: $HOOK_PATH" >&2
    exit 2  # Blocking error
fi

# Check if hook is executable
if [ ! -x "$HOOK_PATH" ]; then
    echo "Error: Hook not executable: $HOOK_PATH" >&2
    exit 2  # Blocking error
fi

# Run the hook with stdin passed through
# This ensures Claude Code's JSON input reaches the hook properly
cat | "$HOOK_PATH" "$@"
exit_code=$?

# Debug info (sent to stderr, won't interfere with JSON passthrough)
echo "Hook executed: $HOOK_NAME from $CLAUDE_DIR (exit: $exit_code)" >&2

exit $exit_code