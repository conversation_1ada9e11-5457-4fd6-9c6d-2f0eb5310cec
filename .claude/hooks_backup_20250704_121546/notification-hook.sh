#!/bin/bash

# Notification Hook for Claude Code
# Properly reads JSO<PERSON> from stdin and sends macOS notifications
# Replaces direct osascript commands to prevent EPIPE errors

# Set error handling
set -o pipefail
trap '' PIPE

# Read JSON input from stdin (REQUIRED for all Claude Code hooks)
if ! JSON_INPUT=$(cat 2>/dev/null); then
    # If we can't read stdin, exit gracefully
    exit 0
fi

# Extract event type and tool name from JSON
EVENT_TYPE=$(echo "$JSON_INPUT" | jq -r '.event // empty' 2>/dev/null)
TOOL_NAME=$(echo "$JSON_INPUT" | jq -r '.toolName // empty' 2>/dev/null)

# Get current project name
CURRENT_DIR=$(pwd)
PROJECT_NAME=$(basename "$CURRENT_DIR")

# If we're in a subdirectory, try to find the project root
if [[ "$CURRENT_DIR" == *"/.claude"* ]] || [[ "$CURRENT_DIR" == */bmad-agent/* ]] || [[ "$CURRENT_DIR" == */layers/* ]] || [[ "$CURRENT_DIR" == */components/* ]]; then
    # Extract project root from path
    PROJECT_ROOT="${CURRENT_DIR%%/.claude*}"
    PROJECT_ROOT="${PROJECT_ROOT%%/bmad-agent*}"
    PROJECT_ROOT="${PROJECT_ROOT%%/layers*}"
    PROJECT_ROOT="${PROJECT_ROOT%%/components*}"
    PROJECT_ROOT="${PROJECT_ROOT%%/app*}"
    PROJECT_NAME=$(basename "$PROJECT_ROOT")
fi

# Get notification parameters from command line args
# Usage: notification-hook.sh "title" "message" "sound"
NOTIFICATION_TITLE="${1:-BMAD-METHOD}"
NOTIFICATION_MESSAGE="${2:-Operation completed}"
NOTIFICATION_SOUND="${3:-Glass}"

# Function to send macOS notification
send_notification() {
    local title="$1"
    local message="$2"
    local sound="$3"
    
    # Use osascript for macOS notifications with error handling
    osascript -e "display notification \"$message\" with title \"$title\" sound name \"$sound\"" 2>/dev/null || true
}

# Determine notification based on context
case "$EVENT_TYPE" in
    "PostToolUse")
        case "$TOOL_NAME" in
            "Write"|"Edit"|"MultiEdit")
                # Code changes detected
                send_notification "BMAD Workflow - $PROJECT_NAME" "Code changes detected - Review cycle may be triggered" "Ping"
                ;;
            *)
                # Generic post-tool notification
                send_notification "$NOTIFICATION_TITLE - $PROJECT_NAME" "$NOTIFICATION_MESSAGE" "$NOTIFICATION_SOUND"
                ;;
        esac
        ;;
    "Stop")
        # Session completed
        send_notification "BMAD-METHOD - $PROJECT_NAME" "Coding session completed" "Glass"
        ;;
    *)
        # Default notification
        send_notification "$NOTIFICATION_TITLE - $PROJECT_NAME" "$NOTIFICATION_MESSAGE" "$NOTIFICATION_SOUND"
        ;;
esac

# CRITICAL: Pass through the JSON for the next hook/tool
# This prevents EPIPE errors
echo "$JSON_INPUT"

# Always exit 0 - notifications are informational only
exit 0