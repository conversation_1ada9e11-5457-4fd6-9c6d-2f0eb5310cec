# Data Models & Analytics

## Data Entities
Core business entities representing the digital business card and advertising platform domain with comprehensive analytics tracking.

### BusinessCard
- **Fields:**
  - id: string - Unique identifier
  - userId: string - Owner user ID  
  - name: string - Contact name (required, max 100 chars)
  - company?: string - Company name
  - title?: string - Job title
  - email?: string - Email address (RFC format validation)
  - phone?: string - Phone number (E.164 format)
  - website?: string - Website URL
  - address?: Address - Physical address object
  - socialLinks?: SocialLinks - Social media profiles
  - imageUrl?: string - Business card image URL
  - qrCodeUrl?: string - Generated QR code URL
  - ocrConfidence?: number - OCR extraction confidence (0-1)
  - tags: string[] - Searchable tags (max 20 items, 30 chars each)
  - isPublic: boolean - Public visibility flag
  - moderationStatus: 'pending' | 'approved' | 'rejected'
  - analytics: AnalyticsMetadata - View/interaction statistics
  - createdAt: string - ISO timestamp
  - updatedAt: string - ISO timestamp
- **Relationships:**
  - Related to UserProfile via userId
  - Related to AnalyticsEvent via contentId

### AnalyticsEvent
- **Fields:**
  - id: string - Unique event identifier
  - userId: string - User who triggered event
  - eventType: EventType - Standardized event type enum
  - contentId?: string - Related content ID
  - sessionId: string - User session identifier
  - timestamp: string - ISO timestamp
  - location?: GeoLocation - Geographic coordinates
  - device: DeviceInfo - Device/browser information
  - metadata: EventMetadata - Event-specific data
  - processingStatus: 'pending' | 'processed' | 'aggregated'
- **Event Types:** 'content_view', 'qr_scan', 'vcard_download', 'contact_action', 'ad_impression', 'ad_click', 'search_query', 'content_share'
- **Relationships:**
  - Related to BusinessCard via contentId
  - Related to UserProfile via userId
  - Aggregated into analytics_aggregations collection

### AdCampaign
- **Fields:**
  - id: string - Campaign identifier
  - advertiserId: string - Advertiser user ID
  - name: string - Campaign name
  - description?: string - Campaign description
  - adSpotIds: string[] - Target ad spot positions
  - targeting: TargetingCriteria - Audience targeting rules
  - budget: Budget - Campaign budget and bidding
  - creative: AdCreative - Advertisement content
  - schedule: CampaignSchedule - Start/end dates and timing
  - status: 'draft' | 'active' | 'paused' | 'completed'
  - performance: CampaignMetrics - Real-time performance data
  - createdAt: string - ISO timestamp
  - updatedAt: string - ISO timestamp
- **Relationships:**
  - Related to UserProfile via advertiserId
  - Related to AdSpot via adSpotIds
  - Generates AnalyticsEvent for impressions/clicks

### UserProfile
- **Fields:**
  - id: string - User identifier (matches Firebase Auth UID)
  - email: string - Primary email address
  - displayName?: string - User display name
  - photoURL?: string - Profile photo URL
  - role: UserRole - System role enum
  - permissions: Permission[] - Granular permissions array
  - teamId?: string - Team/organization association
  - preferences: UserPreferences - User configuration settings
  - subscription: SubscriptionInfo - Premium/enterprise subscription
  - analytics: UserAnalytics - User behavior insights
  - createdAt: string - ISO timestamp
  - lastLoginAt: string - ISO timestamp
  - isActive: boolean - Account status flag
- **Relationships:**
  - Related to BusinessCard via userId (one-to-many)
  - Related to AnalyticsEvent via userId (one-to-many)
  - Related to AdCampaign via advertiserId (one-to-many)

## Data Sources
- **Firestore Collections:** Primary real-time database for all business entities
- **Firebase Storage:** Image and file storage with CDN distribution
- **Redis Cache:** Real-time analytics aggregations and session data
- **External OCR APIs:** Google Cloud Vision for text extraction from images
- **Payment Gateways:** Stripe, PayPal, PayFast transaction data
- **Analytics Providers:** Google Analytics 4 event data
- **Enterprise SSO:** Azure AD, Okta, Google Workspace user directory data

## Data Pipeline
1. **Data Ingestion:** Real-time Firestore writes, batch OCR processing, webhook integrations from payment providers
2. **Data Processing:** Firebase Cloud Functions for event processing, OCR result enhancement, aggregation computation
3. **Data Storage:** Firestore for transactional data, Redis for cached aggregations, Firebase Storage for media
4. **Data Access:** Real-time Firestore subscriptions, REST APIs via Cloud Functions, cached analytics via Redis

## Analytics & ML Models
- **Real-Time Event Tracking:** User interaction analytics with geographic and device context
- **Engagement Funnel Analysis:** Multi-step conversion tracking from card view to contact action
- **Geographic Intelligence:** Location-based interaction mapping and regional performance insights
- **A/B Testing Framework:** Built-in experimentation for content optimization and user experience
- **Predictive Analytics:** User behavior prediction models for content recommendations
- **Content Optimization:** AI-powered suggestions for business card and advertisement improvement
- **Revenue Analytics:** Financial tracking and ROI measurement for advertising campaigns

## Data Security & Privacy
- **PII Handling:** All personally identifiable information encrypted at rest, field-level access controls via Firebase Security Rules
- **Data Retention:** 7-year retention for business records, 2-year for analytics events, user-controlled deletion for personal data
- **Access Controls:** Role-based permissions with Firebase Auth custom claims, team-level data isolation, enterprise SSO integration
- **Compliance:** GDPR compliance for EU users, SOC 2 Type II controls, regular security audits and vulnerability assessments

## Database Collections (Firestore)

### users
- Purpose: User profiles, authentication data, preferences
- Partitioning: By user ID
- Indexes: email, teamId, role, subscription.plan

### business_cards  
- Purpose: Digital business card data with OCR results
- Partitioning: By userId for user-specific queries
- Indexes: userId, isPublic, tags, moderationStatus, createdAt

### analytics_events
- Purpose: Real-time user interaction events
- Partitioning: By date (YYYY-MM-DD) for efficient querying
- Indexes: userId, eventType, timestamp, contentId

### analytics_aggregations
- Purpose: Pre-computed analytics for dashboard performance
- Partitioning: By date and aggregation type
- Indexes: date, userId, contentId, metrics types

### ad_campaigns
- Purpose: Advertising campaign data and performance
- Partitioning: By advertiserId
- Indexes: advertiserId, status, schedule dates, adSpotIds
