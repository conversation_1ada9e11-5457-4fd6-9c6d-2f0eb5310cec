# Technology Stack

## Frontend
- **Framework:** Nuxt.js 3.8.x (Universal Vue.js application framework)
- **UI Library:** Vue.js 3.3.x with Composition API
- **State Management:** Pinia 2.1.x with TypeScript support
- **Styling:** Tailwind CSS 3.3.x + Headless UI 1.7.x for accessibility
- **Build Tools:** Vite 4.4.x with fast HMR and optimized builds

## Backend
- **Language:** TypeScript 5.2.x (primary development language)
- **Framework:** Firebase Cloud Functions (serverless microservices pattern)
- **API Style:** RESTful APIs with event-driven architecture
- **Authentication:** Firebase Auth with enterprise SSO integration (SAML/OIDC)
- **Server/Hosting:** Firebase Hosting with CDN distribution, Node.js 20.x runtime

## Data Storage
- **Primary Database:** Firestore (NoSQL real-time database with offline support)
- **Secondary Storage:** Firebase Storage for files/images with CDN integration
- **Caching:** Redis Cloud 7.x for analytics aggregations and session storage
- **Data Access Pattern:** CQRS with event-driven processing, aggregation patterns for analytics performance

## DevOps & Infrastructure
- **Version Control:** Git with GitHub repository management
- **CI/CD:** GitHub Actions with Firebase deployment integration
- **Deployment:** Firebase Hosting + Functions, auto-scaling serverless deployment
- **Monitoring:** Firebase Performance Monitoring + Google Cloud Logging with structured logs
- **Cloud Provider:** Google Cloud Platform for integrated Firebase ecosystem

## Testing
- **Unit Testing:** Vitest 0.34.x with fast execution and ESM support
- **Integration Testing:** API testing with Firebase emulators
- **E2E Testing:** Playwright 1.38.x for cross-browser automation
- **Test Data Strategy:** Firestore emulator with seed data, mock external API integrations

## Development Practices
- **Code Style:** TypeScript strict mode with comprehensive type definitions
- **Documentation:** Comprehensive architecture documentation with BMAD methodology
- **Code Quality Tools:** ESLint with TypeScript rules, Prettier formatting
- **Package Manager:** pnpm for efficient dependency management

## External Integrations
- **OCR Service:** Google Cloud Vision v1 API for text extraction
- **Payment Processing:** Stripe (primary) + PayPal/PayFast (alternative gateways)
- **Email Service:** SendGrid for transactional email delivery with template management
- **Analytics:** Google Analytics 4 with custom event tracking integration
- **Enterprise SSO:** Azure AD, Okta, Google Workspace SAML/OIDC providers

## Architecture Patterns
- **Monorepo Serverless Pattern:** Single repository with Firebase Cloud Functions for microservice-like decomposition
- **Event-Driven Architecture:** Asynchronous event processing for analytics and user interactions
- **CQRS:** Command Query Responsibility Segregation for optimized read/write operations
- **Aggregation Pattern:** Pre-computed analytics in dedicated Firestore collections
- **Adapter Pattern:** Consistent interfaces for external service integrations (payments, CRM, SSO)
- **Factory Pattern:** Content creation with validation, processing, and storage across different content types
