<script setup lang="ts">
const route = useRoute();
</script>

<template>
  <div class="flex w-full bg-white dark:bg-gray-900 shadow-md py-2 border-t border-gray-200 dark:border-gray-800">
    <div class="w-full flex flex-wrap justify-around max-w-7xl mx-auto">
      <Tooltip text="File manager" side="bottom">
        <template #body>
          <div
            @click="$router.push('/c/file-manager')"
            :class="{
              'border-2 border-[#0072ff] dark:border-[#82aae3]': route.fullPath.includes('file-manager'),
              'bg-blue-50 dark:bg-blue-900/30': route.fullPath.includes('file-manager'),
            }"
            class="relative self-center w-12 h-12 text-center bg-white dark:bg-gray-800 rounded-full text-gray-700 dark:text-gray-300 flex items-center justify-center mx-2 shadow-sm hover:shadow transition-all duration-200 hover:bg-blue-50 dark:hover:bg-blue-900/30"
          >
            <Icon
              name="mdi:folder"
              size="24px"
              class="text-[#0072ff] dark:text-[#82aae3]"
            />
          </div>
        </template>
      </Tooltip>
      <Tooltip text="Business Cards" side="bottom">
        <template #body>
          <div
            @click="$router.push('/businesscards')"
            :class="{
              'border-2 border-[#0072ff] dark:border-[#82aae3]': route.fullPath.includes('businesscards'),
              'bg-blue-50 dark:bg-blue-900/30': route.fullPath.includes('businesscards'),
            }"
            class="relative self-center w-12 h-12 text-center bg-white dark:bg-gray-800 rounded-full text-gray-700 dark:text-gray-300 flex items-center justify-center mx-2 shadow-sm hover:shadow transition-all duration-200 hover:bg-blue-50 dark:hover:bg-blue-900/30"
          >
            <Icon
              name="icon-park-solid:user-business"
              size="24px"
              class="text-[#0072ff] dark:text-[#82aae3]"
            />
          </div>
        </template>
      </Tooltip>
      <!-- <Tooltip text="Flyers" side="bottom">
        <template #body>
          <div
            @click="$router.push('/c/flyers/list')"
            :class="{
              'border-2 border-[#0072ff] dark:border-[#82aae3]': route.fullPath.includes('flyers'),
              'bg-blue-50 dark:bg-blue-900/30': route.fullPath.includes('flyers'),
            }"
            class="relative self-center w-12 h-12 text-center bg-white dark:bg-gray-800 rounded-full text-gray-700 dark:text-gray-300 flex items-center justify-center mx-2 shadow-sm hover:shadow transition-all duration-200 hover:bg-blue-50 dark:hover:bg-blue-900/30"
          >
            <Icon
              name="material-symbols:lab-profile-outline"
              size="24px"
              class="text-[#0072ff] dark:text-[#82aae3]"
            />
          </div>
        </template>
      </Tooltip> -->

      <Tooltip text="Items" side="bottom">
        <template #body>
          <div
            @click="$router.push('/c/items/list')"
            :class="{
              'border-2 border-[#0072ff] dark:border-[#82aae3]': route.fullPath.includes('items'),
              'bg-blue-50 dark:bg-blue-900/30': route.fullPath.includes('items'),
            }"
            class="relative self-center w-12 h-12 text-center bg-white dark:bg-gray-800 rounded-full text-gray-700 dark:text-gray-300 flex items-center justify-center mx-2 shadow-sm hover:shadow transition-all duration-200 hover:bg-blue-50 dark:hover:bg-blue-900/30"
          >
            <Icon
              name="icon-park-outline:ad-product"
              size="24px"
              class="text-[#0072ff] dark:text-[#82aae3]"
            />
          </div>
        </template>
      </Tooltip>
      <Tooltip text="Specials" side="bottom">
        <template #body>
          <div
            @click="$router.push('/c/specials/list')"
            :class="{
              'border-2 border-[#0072ff] dark:border-[#82aae3]': route.fullPath.includes('specials'),
              'bg-blue-50 dark:bg-blue-900/30': route.fullPath.includes('specials'),
            }"
            class="relative self-center w-12 h-12 text-center bg-white dark:bg-gray-800 rounded-full text-gray-700 dark:text-gray-300 flex items-center justify-center mx-2 shadow-sm hover:shadow transition-all duration-200 hover:bg-blue-50 dark:hover:bg-blue-900/30"
          >
            <Icon
              name="streamline:food-cake-candle-birthday-event-special-sweet-cake-bake"
              size="24px"
              class="text-[#0072ff] dark:text-[#82aae3]"
            />
          </div>
        </template>
      </Tooltip>
    </div>
  </div>
</template>
