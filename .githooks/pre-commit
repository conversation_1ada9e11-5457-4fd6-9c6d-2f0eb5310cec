#!/bin/bash

# Git pre-commit hook
# Runs quality checks before allowing commit

echo "🔍 Running pre-commit quality checks..."

# Run the Claude-compatible pre-commit hook
if [ -f ".claude/hooks/pre-commit-strict.sh" ]; then
    echo '{}' | .claude/hooks/pre-commit-strict.sh > /dev/null
    hook_exit_code=$?
    
    if [ $hook_exit_code -ne 0 ]; then
        echo ""
        echo "❌ Pre-commit quality checks failed!"
        echo ""
        echo "📋 See quality report: .claude/last-quality-report.md"
        echo ""
        echo "🔧 Quick fixes:"
        echo "  npm run quality:fix    # Auto-fix linting and formatting"
        echo "  npm run type-check     # Check TypeScript errors"
        echo "  npm run test           # Run tests"
        echo ""
        echo "To bypass this check (not recommended): git commit --no-verify"
        exit 1
    else
        echo "✅ All quality checks passed!"
    fi
else
    echo "⚠️  .claude/hooks/pre-commit-strict.sh not found"
fi

# Run standard pre-commit hooks if available
if command -v pre-commit >/dev/null 2>&1 && [ -f ".pre-commit-config.yaml" ]; then
    echo "🪝 Running pre-commit hooks..."
    pre-commit run --files $(git diff --cached --name-only)
fi

echo "✅ Pre-commit checks complete!"