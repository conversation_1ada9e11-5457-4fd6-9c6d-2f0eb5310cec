<script setup lang="ts">
definePageMeta({
  layout: "covalonic",
});
</script>

<template>
  <div class="min-h-screen font-sans relative overflow-hidden">
    <!-- Dark gradient background -->
    <div class="absolute inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900"></div>

    <!-- Decorative elements -->
    <div class="absolute top-20 right-20 w-96 h-96 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-3xl"></div>
    <div class="absolute bottom-40 left-20 w-80 h-80 bg-gradient-to-br from-purple-400/20 to-blue-400/20 rounded-full blur-3xl"></div>
    <div class="absolute top-1/2 right-1/3 w-64 h-64 bg-gradient-to-br from-red-400/15 to-pink-400/15 rounded-full blur-2xl"></div>

    <div class="relative z-10 px-4 md:px-6 py-4 md:py-8">
      <div class="max-w-screen-xl mx-auto">
        <!-- Header Section -->
        <div class="mb-8">
          <div class="flex items-center space-x-4 mb-4">
            <NuxtLink to="/businesscards" class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600/20 to-blue-700/20 hover:from-blue-600/30 hover:to-blue-700/30 text-blue-400 hover:text-blue-300 font-medium rounded-lg transition-all duration-200 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50 border border-blue-400/20 backdrop-blur-sm">
              <Icon name="mdi:arrow-left" class="mr-2 h-4 w-4" />
              Back to Business Cards
            </NuxtLink>
          </div>
          <h1 class="text-4xl font-bold text-white mb-2">Create Business Card</h1>
          <p class="text-lg text-gray-300">Upload a business card or enter contact information manually</p>
        </div>

        <div class="w-full max-w-4xl mx-auto">
          <businesscards-create :homepage="false" />
        </div>
      </div>
    </div>
  </div>
</template>