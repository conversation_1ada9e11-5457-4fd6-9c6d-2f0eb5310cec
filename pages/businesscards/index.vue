'''<script setup lang="ts">
import { onMounted } from 'vue'
import { useBusinessCards } from '@/composables/useBusinessCards'

// Business Cards page - displays business cards and contact information
const { businessCards, loading, error, loadPublicBusinessCards: loadBusinessCards } = useBusinessCards()

const formatDate = (date) => {
  if (!date) return 'Unknown'
  
  const dateObj = date.toDate ? date.toDate() : new Date(date)
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(dateObj)
}

onMounted(() => {
  loadBusinessCards()
})
</script>''

<template>
  <div class="h-full min-h-screen pb-12 font-sans md:p-4 theme_200">
    <!-- Page Header -->
    <section class="mb-8 rounded-lg overflow-hidden shadow-lg bg-gradient-to-r from-blue-600 to-blue-700 text-white">
      <div class="max-w-screen-xl px-4 py-8 mx-auto">
        <div class="flex flex-col md:flex-row items-center justify-between">
          <div class="mb-6 md:mb-0">
            <h1 class="text-3xl font-bold mb-2">Business Cards</h1>
            <p class="text-blue-100 max-w-xl">
              Discover business professionals and services in your area.
              Connect with local businesses and save their contact information.
            </p>
          </div>
          <div class="flex space-x-4">
            <button
              @click="$router.push('/uploads')"
              class="px-4 py-2 bg-white text-blue-600 rounded-md shadow-md hover:bg-blue-50 transition-colors duration-200 flex items-center"
            >
              <Icon name="mdi:plus" class="mr-1" />
              Add Business Card
            </button>
          </div>
        </div>
      </div>
    </section>

    <!-- Location Settings - Temporarily disabled -->
    <!-- <div class="mb-8 rounded-lg overflow-hidden shadow-md">
      <GoogleUpdate />
    </div> -->

    <!-- Business Cards Grid -->
    <section>
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-2xl font-bold text-gray-800 dark:text-gray-200">
            Available Business Cards
          </h2>
          <div class="flex items-center space-x-2">
            <div class="relative">
              <input
                type="text"
                placeholder="Search business cards..."
                class="pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-gray-200"
              />
              <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <Icon name="mdi:magnify" class="text-gray-400" />
              </div>
            </div>
            <select
              class="px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-gray-200"
            >
              <option value="all">All Categories</option>
              <option value="professional">Professional Services</option>
              <option value="retail">Retail</option>
              <option value="hospitality">Hospitality</option>
            </select>
          </div>
        </div>

        <!-- Loading State -->
        <div v-if="loading" class="flex items-center justify-center py-12">
          <div class="flex items-center space-x-2">
            <Icon name="mdi:loading" class="w-5 h-5 animate-spin text-blue-600" />
            <span class="text-gray-600">Loading business cards...</span>
          </div>
        </div>

        <!-- Error State -->
        <div v-else-if="error" class="text-center py-12">
          <Icon name="mdi:alert-circle" class="w-16 h-16 text-red-400 mx-auto mb-4" />
          <h3 class="text-lg font-medium text-gray-900 mb-2">Error Loading Business Cards</h3>
          <p class="text-gray-500 mb-4">{{ error }}</p>
          <button
            @click="loadBusinessCards"
            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>

        <!-- Empty State -->
        <div v-else-if="businessCards.length === 0" class="text-center py-12">
          <Icon name="mdi:card-account-details-outline" class="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 class="text-lg font-medium text-gray-900 mb-2">No Business Cards Found</h3>
          <p class="text-gray-500 mb-4">
            Get started by adding your first business card or uploading one from your collection.
          </p>
          <button
            @click="$router.push('/uploads')"
            class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Add Business Card
          </button>
        </div>

        <!-- Real Business Cards Grid -->
        <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div
            v-for="card in businessCards"
            :key="card.id"
            @click="$router.push(`/businesscards/${card.id}`)"
          >
            <BusinesscardsDarkCard :card="card" />
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

