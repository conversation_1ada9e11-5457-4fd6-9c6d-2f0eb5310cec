<script setup lang="ts">
import { ref, computed } from 'vue';
import { useState, useRouter } from '#app';

definePageMeta({
});

const businesscards = useState("businesscards", () => {
  return [];
});

const filter = ref("");

const businesscardsFilter = computed(() => {
  // business cards with name
  let cards = businesscards.value.filter((item: any) => item.name);
  return cards.filter((item: any) => item.name.toLowerCase().includes(filter.value.toLowerCase()));
});

const selectedBusinessCard: any = useState("selectedBusinessCard", () => {
  return "";
});

const selectCard = (card: any) => {
  selectedBusinessCard.value = card;
  useRouter().push(`/businesscards/${card.id}`);
};
</script>

<template>
  <div class="h-full min-h-screen pb-12 font-sans md:p-4 bg-white dark:bg-gray-800">
    <!-- Header Section -->
    <div class="mb-6">
      <h1 class="text-2.5rem font-bold text-gray-800 dark:text-white mb-2">Business Cards</h1>
      <p class="text-base text-gray-600 dark:text-gray-300">Browse and manage your business card collection</p>
    </div>

    <!-- Search and Filter Section -->
    <div class="flex w-full mb-6 bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
      <div class="relative flex-grow">
        <input
          v-model="filter"
          class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-gray-200"
          placeholder="Search business cards..."
        />
        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
      </div>
      <button
        @click="$router.push('/uploads')"
        class="ml-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200 flex items-center"
      >
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
        </svg>
        Add New
      </button>
    </div>

    <!-- Business Cards Grid -->
    <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      <div
        v-for="(card, index) in businesscardsFilter"
        :key="index"
        class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-200 cursor-pointer h-96"
        @click="selectCard(card)"
      >
        <div v-if="card.image && card.image.src" class="h-48 overflow-hidden">
          <img :src="card.image.src" alt="Business Card" class="w-full h-full object-cover">
        </div>
        <div v-else class="h-48 bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-blue-600 dark:text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        </div>
        <div class="p-4">
          <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-1">{{ card.name }}</h3>
          <p class="text-sm text-gray-500 dark:text-gray-400 mb-2">{{ card.company || 'No company' }}</p>
          <p v-if="card.email" class="text-sm text-gray-600 dark:text-gray-300 truncate">{{ card.email }}</p>
          <p v-if="card.phone" class="text-sm text-gray-600 dark:text-gray-300 truncate">{{ card.phone }}</p>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="businesscardsFilter.length === 0" class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-8 text-center mt-6">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
      </svg>
      <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-2">No business cards found</h3>
      <p class="text-gray-500 dark:text-gray-400 mb-4">Try adjusting your search or add a new business card.</p>
      <button
        @click="$router.push('/uploads')"
        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200"
      >
        Add New Business Card
      </button>
    </div>
  </div>
</template>