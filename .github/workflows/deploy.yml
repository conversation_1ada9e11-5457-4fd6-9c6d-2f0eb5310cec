name: Deploy to Firebase

on:
  push:
    branches:
      - main
      - staging
      - development
  pull_request:
    types: [opened, synchronize, reopened]

env:
  NODE_VERSION: '18'

jobs:
  test:
    name: Test
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run linter
        run: npm run lint --if-present

      - name: Run tests
        run: npm run test

      - name: Build application
        run: npm run build

  deploy-development:
    name: Deploy to Development
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/development'
    environment: development
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Copy environment file
        run: cp .env.development .env

      - name: Build for development
        run: npm run build
        env:
          NUXT_PUBLIC_ENVIRONMENT: development

      - name: Deploy to Firebase Development
        uses: w9jds/firebase-action@v13.0.3
        with:
          args: deploy --only hosting,firestore,storage,functions --project development
        env:
          FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}

  deploy-staging:
    name: Deploy to Staging
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/staging'
    environment: staging
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Copy environment file
        run: cp .env.staging .env

      - name: Build for staging
        run: npm run build
        env:
          NUXT_PUBLIC_ENVIRONMENT: staging

      - name: Deploy to Firebase Staging
        uses: w9jds/firebase-action@v13.0.3
        with:
          args: deploy --only hosting,firestore,storage,functions --project staging
        env:
          FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}

  deploy-production:
    name: Deploy to Production
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Copy environment file
        run: cp .env.production .env

      - name: Build for production
        run: npm run build
        env:
          NUXT_PUBLIC_ENVIRONMENT: production

      - name: Deploy to Firebase Production
        uses: w9jds/firebase-action@v13.0.3
        with:
          args: deploy --only hosting,firestore,storage,functions --project production
        env:
          FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}

      - name: Create Release
        if: success()
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: v${{ github.run_number }}
          release_name: Release v${{ github.run_number }}
          body: |
            Production deployment completed successfully.
            
            Commit: ${{ github.sha }}
            Author: ${{ github.actor }}
          draft: false
          prerelease: false