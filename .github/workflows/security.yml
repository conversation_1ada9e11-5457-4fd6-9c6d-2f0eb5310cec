name: Security Checks

on:
  push:
    branches: [main, staging, development]
  pull_request:
    branches: [main, staging, development]
  schedule:
    # Run security checks every Monday at 9 AM UTC
    - cron: '0 9 * * 1'

jobs:
  security:
    name: Security Scan
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'
          severity: 'CRITICAL,HIGH'

      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

  dependency-check:
    name: Dependency Check
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18

      - name: Install dependencies
        run: npm ci

      - name: Run npm audit
        run: npm audit --production

      - name: Check for outdated packages
        run: npm outdated || true

  firebase-rules-test:
    name: Firebase Security Rules Test
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18

      - name: Install Firebase tools
        run: npm install -g firebase-tools

      - name: Test Firestore rules
        run: |
          cd functions
          npm ci
          cd ..
          firebase emulators:exec --only firestore "npm run test:rules" --project covalonic-dev
        env:
          FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}

  code-quality:
    name: Code Quality Check
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18

      - name: Install dependencies
        run: npm ci

      - name: Run ESLint
        run: npm run lint --if-present

      - name: Check TypeScript
        run: npx tsc --noEmit --skipLibCheck