# Enhanced Analytics Implementation Plan - Phase 1

## Overview
This document outlines the detailed implementation plan for Phase 1 of the Enhanced Analytics feature: Enhanced Data Collection. This phase focuses on expanding tracking events, improving the data structure, and implementing real-time tracking capabilities.

## Current Implementation Analysis

### Current Analytics Structure
- **Stats Collection**: Firebase Cloud Functions update stats collections in Firestore when business cards are created, updated, or deleted
- **Stats Schema**: Stats are stored in Firestore with the following structure:
  - `stats/{month-year}/business/{business_id}` - Monthly business stats
  - `stats/{month-year}/user/{user_id}` - Monthly user stats
  - `stats/{date}/business/{business_id}` - Daily business stats
  - `stats/total/business/{business_id}` - Total business stats
  - `stats/total/user/{user_id}` - Total user stats
- **Tracking Events**: Currently only tracks basic events like business card creation
- **Admin Dashboard**: Simple metrics display with limited visualization
- **User Analytics**: Minimal analytics shown to users about their business cards

### Limitations of Current Implementation
1. Limited tracking of business card interactions (only views, not downloads or QR scans)
2. No tracking of time spent viewing cards or user actions
3. No geographic data collection
4. Inefficient data structure for complex queries
5. No real-time updates for analytics data
6. Limited visualization capabilities

## Implementation Plan for Phase 1

### 1. Create Enhanced Analytics Tracking Service

#### File: `composables/analytics-tracking.ts`
```typescript
/**
 * Composable for tracking analytics events
 */
export const useAnalyticsTracking = () => {
  const { firestoreDb } = useFirebase()
  const { currentUser } = useAuth()
  
  /**
   * Track a business card view event
   * @param cardId The ID of the business card
   * @param source The source of the view (direct, search, etc.)
   */
  const trackCardView = async (cardId: string, source: string = 'direct') => {
    try {
      // Get device and browser information
      const device = getDeviceInfo()
      
      // Get location if available
      const location = await getLocationIfPermitted()
      
      // Create view event document
      const viewEvent = {
        card_id: cardId,
        user_id: currentUser.value?.id || 'anonymous',
        timestamp: new Date(),
        source,
        device,
        location,
        session_id: getSessionId(),
        duration_start: new Date()
      }
      
      // Store the event in Firestore
      await addDoc(collection(firestoreDb, 'analytics_events'), viewEvent)
      
      // Update the view counter in the business card document
      await updateDoc(doc(firestoreDb, 'businesscards', cardId), {
        view_count: increment(1),
        last_viewed: new Date()
      })
      
      // Start tracking view duration
      startDurationTracking(cardId)
      
      return true
    } catch (error) {
      console.error('Error tracking card view:', error)
      return false
    }
  }
  
  /**
   * Track a business card download event
   * @param cardId The ID of the business card
   * @param format The format of the download (vcard, pdf, etc.)
   */
  const trackCardDownload = async (cardId: string, format: string = 'vcard') => {
    try {
      // Create download event document
      const downloadEvent = {
        card_id: cardId,
        user_id: currentUser.value?.id || 'anonymous',
        timestamp: new Date(),
        format,
        device: getDeviceInfo(),
        location: await getLocationIfPermitted(),
        session_id: getSessionId()
      }
      
      // Store the event in Firestore
      await addDoc(collection(firestoreDb, 'analytics_events'), downloadEvent)
      
      // Update the download counter in the business card document
      await updateDoc(doc(firestoreDb, 'businesscards', cardId), {
        download_count: increment(1),
        last_downloaded: new Date()
      })
      
      return true
    } catch (error) {
      console.error('Error tracking card download:', error)
      return false
    }
  }
  
  /**
   * Track a QR code scan event
   * @param cardId The ID of the business card
   * @param qrType The type of QR code (contact, website, etc.)
   */
  const trackQRScan = async (cardId: string, qrType: string = 'contact') => {
    try {
      // Create QR scan event document
      const qrScanEvent = {
        card_id: cardId,
        user_id: currentUser.value?.id || 'anonymous',
        timestamp: new Date(),
        qr_type: qrType,
        device: getDeviceInfo(),
        location: await getLocationIfPermitted(),
        session_id: getSessionId()
      }
      
      // Store the event in Firestore
      await addDoc(collection(firestoreDb, 'analytics_events'), qrScanEvent)
      
      // Update the QR scan counter in the business card document
      await updateDoc(doc(firestoreDb, 'businesscards', cardId), {
        qr_scan_count: increment(1),
        last_qr_scanned: new Date()
      })
      
      return true
    } catch (error) {
      console.error('Error tracking QR scan:', error)
      return false
    }
  }
  
  /**
   * Track a contact action event (email click, phone call, etc.)
   * @param cardId The ID of the business card
   * @param actionType The type of action (email, phone, website, etc.)
   */
  const trackContactAction = async (cardId: string, actionType: string) => {
    try {
      // Create contact action event document
      const contactActionEvent = {
        card_id: cardId,
        user_id: currentUser.value?.id || 'anonymous',
        timestamp: new Date(),
        action_type: actionType,
        device: getDeviceInfo(),
        location: await getLocationIfPermitted(),
        session_id: getSessionId()
      }
      
      // Store the event in Firestore
      await addDoc(collection(firestoreDb, 'analytics_events'), contactActionEvent)
      
      // Update the action counter in the business card document
      await updateDoc(doc(firestoreDb, 'businesscards', cardId), {
        [`${actionType}_count`]: increment(1),
        last_action: new Date()
      })
      
      return true
    } catch (error) {
      console.error('Error tracking contact action:', error)
      return false
    }
  }
  
  // Helper functions
  const getDeviceInfo = () => {
    if (process.client) {
      return {
        user_agent: navigator.userAgent,
        platform: navigator.platform,
        screen_size: `${window.screen.width}x${window.screen.height}`,
        viewport_size: `${window.innerWidth}x${window.innerHeight}`,
        is_mobile: /iPhone|iPad|iPod|Android/i.test(navigator.userAgent)
      }
    }
    return {}
  }
  
  const getLocationIfPermitted = async () => {
    if (process.client && navigator.geolocation) {
      try {
        const position = await new Promise((resolve, reject) => {
          navigator.geolocation.getCurrentPosition(resolve, reject, {
            timeout: 5000,
            maximumAge: 60000
          })
        })
        
        return {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy
        }
      } catch (error) {
        console.log('Location permission denied or error:', error)
        return null
      }
    }
    return null
  }
  
  const getSessionId = () => {
    if (process.client) {
      let sessionId = localStorage.getItem('analytics_session_id')
      if (!sessionId) {
        sessionId = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}`
        localStorage.setItem('analytics_session_id', sessionId)
      }
      return sessionId
    }
    return null
  }
  
  const startDurationTracking = (cardId: string) => {
    if (process.client) {
      localStorage.setItem(`view_start_${cardId}`, Date.now().toString())
      
      // Add event listener for page unload to track duration
      window.addEventListener('beforeunload', () => {
        const startTime = localStorage.getItem(`view_start_${cardId}`)
        if (startTime) {
          const duration = Date.now() - parseInt(startTime)
          
          // Use sendBeacon for reliable data sending during page unload
          const data = JSON.stringify({
            card_id: cardId,
            user_id: currentUser.value?.id || 'anonymous',
            duration,
            timestamp: new Date()
          })
          
          navigator.sendBeacon('/api/analytics/track-duration', data)
          localStorage.removeItem(`view_start_${cardId}`)
        }
      })
    }
  }
  
  return {
    trackCardView,
    trackCardDownload,
    trackQRScan,
    trackContactAction
  }
}
```

### 2. Create Server API Endpoint for Duration Tracking

#### File: `server/api/analytics/track-duration.post.ts`
```typescript
import { serverSupabaseClient } from '#supabase/server'

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    const { card_id, user_id, duration, timestamp } = body
    
    // Validate required fields
    if (!card_id || !duration) {
      return { success: false, error: 'Missing required fields' }
    }
    
    // Add duration event to Firestore
    const db = getFirestore()
    await addDoc(collection(db, 'analytics_events'), {
      card_id,
      user_id: user_id || 'anonymous',
      event_type: 'view_duration',
      duration,
      timestamp: new Date(timestamp)
    })
    
    // Update average duration in business card document
    const cardRef = doc(db, 'businesscards', card_id)
    const cardDoc = await getDoc(cardRef)
    
    if (cardDoc.exists()) {
      const cardData = cardDoc.data()
      const currentAvg = cardData.avg_view_duration || 0
      const viewCount = cardData.view_count || 1
      
      // Calculate new average duration
      const newAvg = ((currentAvg * (viewCount - 1)) + duration) / viewCount
      
      await updateDoc(cardRef, {
        avg_view_duration: newAvg,
        total_view_duration: increment(duration)
      })
    }
    
    return { success: true }
  } catch (error) {
    console.error('Error tracking duration:', error)
    return { success: false, error: error.message }
  }
})
```

### 3. Create Data Aggregation Cloud Function

#### File: `functions/analytics/index.js`
```javascript
const functions = require('firebase-functions')
const admin = require('firebase-admin')
const moment = require('moment')

/**
 * Aggregates analytics events into daily summaries
 * Runs every hour to process new events
 */
exports.aggregateAnalyticsEvents = functions.pubsub
  .schedule('every 60 minutes')
  .onRun(async (context) => {
    const db = admin.firestore()
    
    // Get the timestamp for one hour ago
    const oneHourAgo = new Date()
    oneHourAgo.setHours(oneHourAgo.getHours() - 1)
    
    // Query for events in the last hour that haven't been aggregated
    const eventsSnapshot = await db.collection('analytics_events')
      .where('timestamp', '>=', oneHourAgo)
      .where('aggregated', '==', false)
      .limit(500) // Process in batches to avoid timeout
      .get()
    
    if (eventsSnapshot.empty) {
      console.log('No new events to aggregate')
      return null
    }
    
    console.log(`Aggregating ${eventsSnapshot.size} events`)
    
    // Group events by card_id and date
    const eventsByCardAndDate = {}
    
    eventsSnapshot.forEach(doc => {
      const event = doc.data()
      const eventId = doc.id
      const cardId = event.card_id
      const date = moment(event.timestamp.toDate()).format('YYYY-MM-DD')
      const eventType = event.event_type || 'view' // Default to view
      
      if (!eventsByCardAndDate[cardId]) {
        eventsByCardAndDate[cardId] = {}
      }
      
      if (!eventsByCardAndDate[cardId][date]) {
        eventsByCardAndDate[cardId][date] = {
          views: 0,
          downloads: 0,
          qr_scans: 0,
          email_clicks: 0,
          phone_calls: 0,
          website_visits: 0,
          total_duration: 0,
          unique_users: new Set(),
          events: []
        }
      }
      
      // Add event to the list
      eventsByCardAndDate[cardId][date].events.push(eventId)
      
      // Add unique user
      if (event.user_id) {
        eventsByCardAndDate[cardId][date].unique_users.add(event.user_id)
      }
      
      // Update metrics based on event type
      switch (eventType) {
        case 'view':
          eventsByCardAndDate[cardId][date].views++
          break
        case 'download':
          eventsByCardAndDate[cardId][date].downloads++
          break
        case 'qr_scan':
          eventsByCardAndDate[cardId][date].qr_scans++
          break
        case 'view_duration':
          eventsByCardAndDate[cardId][date].total_duration += (event.duration || 0)
          break
        case 'contact_action':
          const actionType = event.action_type
          if (actionType === 'email') {
            eventsByCardAndDate[cardId][date].email_clicks++
          } else if (actionType === 'phone') {
            eventsByCardAndDate[cardId][date].phone_calls++
          } else if (actionType === 'website') {
            eventsByCardAndDate[cardId][date].website_visits++
          }
          break
      }
    })
    
    // Update aggregated stats and mark events as aggregated
    const batch = db.batch()
    
    for (const cardId in eventsByCardAndDate) {
      for (const date in eventsByCardAndDate[cardId]) {
        const stats = eventsByCardAndDate[cardId][date]
        const uniqueUsers = Array.from(stats.unique_users)
        
        // Update daily stats
        const dailyStatsRef = db.collection('analytics_daily')
          .doc(`${cardId}_${date}`)
        
        batch.set(dailyStatsRef, {
          card_id: cardId,
          date,
          views: admin.firestore.FieldValue.increment(stats.views),
          downloads: admin.firestore.FieldValue.increment(stats.downloads),
          qr_scans: admin.firestore.FieldValue.increment(stats.qr_scans),
          email_clicks: admin.firestore.FieldValue.increment(stats.email_clicks),
          phone_calls: admin.firestore.FieldValue.increment(stats.phone_calls),
          website_visits: admin.firestore.FieldValue.increment(stats.website_visits),
          total_duration: admin.firestore.FieldValue.increment(stats.total_duration),
          unique_users: admin.firestore.FieldValue.arrayUnion(...uniqueUsers),
          last_updated: admin.firestore.FieldValue.serverTimestamp()
        }, { merge: true })
        
        // Mark events as aggregated
        stats.events.forEach(eventId => {
          const eventRef = db.collection('analytics_events').doc(eventId)
          batch.update(eventRef, { aggregated: true })
        })
      }
    }
    
    await batch.commit()
    console.log('Analytics aggregation completed successfully')
    return null
  })
```

### 4. Modify Business Card Components to Track Events

#### File: `components/businesscards/view.vue`
```typescript
// Add to the existing imports
import { useAnalyticsTracking } from '~/composables/analytics-tracking'

// Add inside the setup function
const { trackCardView } = useAnalyticsTracking()

// Add to onMounted hook
onMounted(async () => {
  if (viewProps.user) {
    businesscardsSelect.value = viewProps.user
    dataselect.value = viewProps.user
    
    // Track the view event
    await trackCardView(viewProps.user.id, 'direct')
  }
})
```

#### File: `components/businesscards/vcard.vue`
```typescript
// Add to the existing imports
import { useAnalyticsTracking } from '~/composables/analytics-tracking'

// Add inside the setup function
const { trackCardView, trackCardDownload, trackContactAction } = useAnalyticsTracking()

// Modify the getCalendarBookings function
const getCalendarBookings = async () => {
  console.log('GET CARD id', routeId.params.id)
  const cards = await queryById("businesscards", routeId.params.id)
  console.log("cards", cards)
  console.log("cards result", cards)

  if (cards) {
    currentClient.value = cards
    getSpace()
    
    // Track the view event
    await trackCardView(routeId.params.id, 'vcard')
  }
}

// Add tracking to the download function
const downloadVCard = async () => {
  // Existing download code...
  
  // Track the download event
  await trackCardDownload(routeId.params.id, 'vcard')
}

// Add tracking to contact actions
const handleEmailClick = async (email) => {
  await trackContactAction(routeId.params.id, 'email')
  window.location.href = `mailto:${email}`
}

const handlePhoneClick = async (phone) => {
  await trackContactAction(routeId.params.id, 'phone')
  window.location.href = `tel:${phone}`
}

const handleWebsiteClick = async (website) => {
  await trackContactAction(routeId.params.id, 'website')
  window.open(website, '_blank')
}
```

### 5. Create Firestore Security Rules for Analytics Data

#### File: `firestore.rules` (add to existing rules)
```
match /analytics_events/{eventId} {
  allow read: if isAuthenticated() && (isAdmin() || isOwner(resource.data.user_id));
  allow create: if true; // Allow anonymous event creation
  allow update, delete: if isAdmin();
}

match /analytics_daily/{docId} {
  allow read: if isAuthenticated() && (isAdmin() || isCardOwner(resource.data.card_id));
  allow write: if isAdmin();
}

function isCardOwner(cardId) {
  return exists(/databases/$(database)/documents/businesscards/$(cardId)) &&
    get(/databases/$(database)/documents/businesscards/$(cardId)).data.created_by == request.auth.uid;
}
```

## Testing Plan

1. **Unit Tests**
   - Test analytics tracking functions
   - Test data aggregation logic
   - Test duration calculation

2. **Integration Tests**
   - Test event creation and storage
   - Test aggregation function
   - Test security rules

3. **Manual Testing**
   - Test business card view tracking
   - Test download tracking
   - Test QR code scan tracking
   - Test contact action tracking
   - Test duration tracking

## Implementation Timeline

1. **Week 1 (Days 1-3)**: Create analytics tracking service
2. **Week 1 (Days 4-5)**: Implement server API endpoint for duration tracking
3. **Week 2 (Days 1-3)**: Create data aggregation Cloud Function
4. **Week 2 (Days 4-5)**: Modify business card components to track events
5. **Week 3 (Days 1-2)**: Create Firestore security rules
6. **Week 3 (Days 3-5)**: Testing and bug fixes

## Success Criteria

1. All business card interactions (views, downloads, QR scans, contact actions) are tracked
2. View duration is accurately measured and stored
3. Geographic data is collected when permitted
4. Data is efficiently aggregated for performance
5. Real-time tracking is implemented
6. Security rules protect analytics data appropriately
