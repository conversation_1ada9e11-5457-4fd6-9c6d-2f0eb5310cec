# Covalonic UI Guidelines

## Overview
This document provides comprehensive UI guidelines for the Covalonic platform to ensure a consistent, professional, and user-friendly experience across all components and pages. These guidelines should be followed for all new development and UI enhancements.

## Brand Identity

### Logo Usage
- The Covalonic logo should be displayed prominently in the navigation bar
- Maintain proper spacing around the logo (minimum 12px padding)
- The logo should link to the homepage
- Logo size: 40-48px height in desktop view, 32-36px in mobile view

### Brand Colors

#### Primary Colors
- **Primary Blue**: `rgb(0, 114, 255)` / `#0072ff` - Main brand color for primary actions and key UI elements
- **Primary Blue Light**: `rgb(130, 170, 227)` / `#82aae3` - Secondary brand color for backgrounds and accents
- **Primary Blue Dark**: `rgb(0, 84, 187)` / `#0054bb` - Used for hover states and emphasis

#### Secondary Colors
- **Success Green**: `rgb(0, 230, 10)` / `#00e60a` - Used for success states and confirmations
- **Warning Yellow**: `rgb(255, 204, 0)` / `#ffcc00` - Used for warnings and alerts
- **Error Red**: `rgb(255, 0, 0)` / `#ff0000` - Used for errors and critical alerts
- **Info Blue**: `rgb(0, 213, 255)` / `#00d5ff` - Used for informational states

#### Neutral Colors
- **White**: `rgb(255, 255, 255)` / `#ffffff` - Primary background (light mode)
- **Light Gray**: `rgb(245, 245, 255)` / `#f5f5ff` - Secondary background (light mode)
- **Medium Gray**: `rgb(89, 89, 131)` / `#595983` - Text color (light mode)
- **Dark Gray**: `rgb(33, 33, 33)` / `#212121` - Text color (dark mode)
- **Black**: `rgb(3, 7, 9)` / `#030709` - Primary background (dark mode)

### Typography

#### Font Family
- **Primary Font**: 'Nunito', sans-serif - Used for all text
- **Fallback Fonts**: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif

#### Font Sizes
- **Heading 1**: 2.5rem (40px) - Page titles
- **Heading 2**: 2rem (32px) - Section titles
- **Heading 3**: 1.5rem (24px) - Subsection titles
- **Heading 4**: 1.25rem (20px) - Card titles
- **Body**: 1rem (16px) - Regular text
- **Small**: 0.875rem (14px) - Secondary text
- **Extra Small**: 0.75rem (12px) - Labels and captions

#### Font Weights
- **Regular**: 400 - Body text
- **Medium**: 500 - Emphasis
- **Semibold**: 600 - Subheadings
- **Bold**: 700 - Headings and important text
- **Extrabold**: 800 - Used sparingly for extra emphasis

## Component Guidelines

### Buttons

#### Primary Button
- Blue background (`#0072ff`)
- White text
- Rounded corners (6px border-radius)
- Hover state: Slightly darker blue (`#0054bb`)
- Active state: Even darker blue with slight inset shadow
- Disabled state: Reduced opacity (0.6)
- Example class: `px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200`

#### Secondary Button
- White background
- Blue text and border
- Rounded corners (6px border-radius)
- Hover state: Light blue background
- Example class: `px-4 py-2 border border-blue-600 text-blue-600 bg-white rounded-md hover:bg-blue-50 transition-colors duration-200`

#### Text Button
- No background
- Blue text
- Hover state: Light blue background
- Example class: `px-4 py-2 text-blue-600 hover:bg-blue-50 rounded-md transition-colors duration-200`

#### Button Sizes
- **Small**: px-3 py-1 text-sm
- **Medium** (default): px-4 py-2
- **Large**: px-6 py-3 text-lg

### Cards
- White background (light mode) / Dark gray background (dark mode)
- Subtle shadow
- Rounded corners (8px border-radius)
- Consistent padding (16px or 24px)
- Optional header with title
- Example class: `bg-white dark:bg-gray-800 rounded-lg shadow-md p-6`

### Form Elements

#### Text Inputs
- White background (light mode) / Dark gray background (dark mode)
- Gray border (1px)
- Rounded corners (6px border-radius)
- Focus state: Blue border with ring
- Example class: `w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-gray-200`

#### Labels
- Positioned above inputs
- Medium font weight (500)
- Small margin below (4px)
- Example class: `block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1`

#### Checkboxes and Radio Buttons
- Custom styling with brand colors
- Clear focus states
- Adequate spacing for touch targets

### Navigation

#### Main Navigation
- Sticky positioning at the top
- White background (light mode) / Dark background (dark mode)
- Subtle shadow
- Clear hover and active states
- Mobile-responsive with hamburger menu

#### Tab Navigation
- Underline style for active tab
- Clear hover states
- Consistent spacing between tabs
- Example class: `px-3 py-2 border-b-2 border-transparent hover:border-blue-300 hover:text-blue-600`
- Active tab class: `px-3 py-2 border-b-2 border-blue-600 text-blue-600`

## Layout Guidelines

### Spacing System
- **Extra Small**: 0.25rem (4px)
- **Small**: 0.5rem (8px)
- **Medium**: 1rem (16px)
- **Large**: 1.5rem (24px)
- **Extra Large**: 2rem (32px)
- **2x Extra Large**: 3rem (48px)

### Grid System
- Use CSS Grid and Flexbox for layouts
- Responsive grid with appropriate breakpoints
- Consistent gutters between grid items (16px or 24px)

### Responsive Breakpoints
- **Small**: 640px and up (`sm:`)
- **Medium**: 768px and up (`md:`)
- **Large**: 1024px and up (`lg:`)
- **Extra Large**: 1280px and up (`xl:`)
- **2x Extra Large**: 1536px and up (`2xl:`)

## UI Patterns

### Cards and Lists
- Use cards for discrete content blocks
- Use lists for sequential or related items
- Maintain consistent spacing between items
- Ensure proper hierarchy with headings and subheadings

### Forms
- Group related fields together
- Use clear labels and helpful placeholder text
- Provide validation feedback inline
- Use consistent button placement (typically right-aligned)

### Modals and Dialogs
- Center on screen with overlay background
- Clear close button
- Descriptive title
- Focused content with limited options
- Example class: `fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50`

### Notifications and Alerts
- Use appropriate colors for different types (success, warning, error, info)
- Clear and concise messaging
- Dismissible when appropriate
- Positioned consistently (typically top-right)

## Accessibility Guidelines

### Color Contrast
- Maintain WCAG 2.1 AA compliance (minimum 4.5:1 for normal text, 3:1 for large text)
- Don't rely solely on color to convey information
- Test color combinations in both light and dark modes

### Focus States
- Clearly visible focus indicators for keyboard navigation
- Don't remove focus outlines without providing alternatives
- Ensure logical tab order

### Screen Readers
- Use semantic HTML elements
- Provide appropriate ARIA attributes when necessary
- Include alt text for images
- Ensure form elements have associated labels

## Dark Mode

### Implementation
- Use Tailwind's dark mode classes (`dark:`)
- Test all components in both light and dark modes
- Ensure sufficient contrast in both modes

### Color Adjustments
- Invert background and text colors
- Adjust accent colors for better visibility
- Reduce brightness and increase contrast

## Animation and Transitions

### Principles
- Use animations purposefully, not decoratively
- Keep animations subtle and quick
- Respect user preferences for reduced motion

### Common Transitions
- **Duration**: 200-300ms for most transitions
- **Easing**: ease-in-out for smooth transitions
- **Properties**: opacity, transform, background-color

## Implementation Notes

1. Use Tailwind CSS classes for most styling needs
2. Follow mobile-first approach
3. Maintain dark mode compatibility
4. Keep accessibility in mind
5. Test across all breakpoints
6. Use animations sparingly and purposefully
