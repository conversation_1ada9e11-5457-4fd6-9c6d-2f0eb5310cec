# Covalonic Styling Guide

## Overview
This document defines the styling standards for the Covalonic platform to ensure consistency across all components and pages. It covers color schemes, typography, component styling, spacing, and responsive design principles.

## Brand Colors

### Primary Colors
- **Primary**: `rgb(130, 170, 227)` - Main brand color, used for primary actions and key UI elements
- **Primary Focus**: `rgb(145, 216, 228)` - Used for hover/focus states of primary elements
- **Primary Content**: `rgb(255, 255, 255)` - Text color on primary backgrounds

### Secondary Colors
- **Secondary**: `rgb(0, 230, 10)` - Used for secondary actions and success states
- **Secondary Focus**: `rgb(0, 255, 155)` - Used for hover/focus states of secondary elements
- **Accent**: `rgb(0, 213, 255)` - Used for highlighting important elements

### Neutral Colors
- **Base 100**: `rgb(255, 255, 255)` - Main background color (light mode)
- **Base 200**: `rgb(245, 245, 255)` - Secondary background color (light mode)
- **Base 300**: `rgb(245, 245, 245)` - Tertiary background color (light mode)
- **Base Content**: `rgb(89, 89, 131)` - Main text color (light mode)

### Dark Mode Colors
- **Base Dark 100**: `rgb(3, 7, 9)` - Main background color (dark mode)
- **Base Dark 200**: `rgb(12, 17, 20)` - Secondary background color (dark mode)
- **Base Dark 300**: `rgb(33, 33, 33)` - Tertiary background color (dark mode)
- **Base Dark Content**: `rgb(152, 165, 192)` - Main text color (dark mode)

### Utility Colors
- **Success**: `rgb(0, 230, 10)` - Success states and confirmations
- **Warning**: `rgb(255, 204, 0)` - Warning states and alerts
- **Error**: `rgb(255, 0, 0)` - Error states and critical alerts
- **Info**: `rgb(0, 213, 255)` - Informational states and notices

## Typography

### Font Family
- **Primary Font**: 'Nunito', sans-serif - Used for all text
- **Secondary Font**: 'Merriweather', serif - Used for headings when needed

### Font Sizes
- **Heading 1**: 2rem (32px) - Page titles
- **Heading 2**: 1.5rem (24px) - Section titles
- **Heading 3**: 1.25rem (20px) - Subsection titles
- **Body**: 1rem (16px) - Regular text
- **Small**: 0.875rem (14px) - Secondary text
- **Extra Small**: 0.75rem (12px) - Labels and captions

### Font Weights
- **Regular**: 400 - Body text
- **Medium**: 500 - Emphasis
- **Semibold**: 600 - Subheadings
- **Bold**: 700 - Headings and important text
- **Extrabold**: 800 - Used sparingly for extra emphasis

## Component Styling

### Buttons
- **Primary Button**: `.o_btn_primarys` - Blue background, white text
- **Secondary Button**: `.o_btn_secondary` - Green background, white text
- **Outline Button**: `.o_btn_primarys_border` - Transparent background, blue border and text
- **Text Button**: `.o_btn_text` - No background, colored text
- **Disabled Button**: Add `disabled` attribute - Reduced opacity, gray background

### Cards
- **Standard Card**: `.o_card` - White background, subtle shadow
- **Soft Card**: `.o_card_soft` - Slightly darker background, more pronounced shadow
- **Interactive Card**: `.o_card` with hover effects - Elevates on hover

### Form Elements
- **Input**: `.o_input` - White background, subtle border
- **Label**: `.o_label` - Semibold text above inputs
- **Small Label**: `.o_label_small` - Smaller text for compact forms
- **Checkbox/Radio**: Custom styling with appropriate colors

### Navigation
- **Main Navigation**: Dark background in dark mode, light in light mode
- **Tab Navigation**: Underline for active tab, hover effects for inactive
- **Breadcrumbs**: Small text with separators

## Spacing System

### Margins and Padding
- **Extra Small**: 0.25rem (4px)
- **Small**: 0.5rem (8px)
- **Medium**: 1rem (16px)
- **Large**: 1.5rem (24px)
- **Extra Large**: 2rem (32px)
- **2x Extra Large**: 3rem (48px)

### Layout Spacing
- **Container Padding**: 1rem (16px) on small screens, 2rem (32px) on larger screens
- **Section Spacing**: 2rem (32px) between major sections
- **Component Spacing**: 1rem (16px) between related components

## Theme Classes

### Light/Dark Mode
- **Theme 100**: `.theme_100` - Primary background with appropriate text color
- **Theme 200**: `.theme_200` - Secondary background with appropriate text color
- **Theme 300**: `.theme_300` - Tertiary background with appropriate text color

### Contextual Themes
- **Theme Primary**: `.theme_primarys` - Primary color background with appropriate text
- **Theme Success**: `.theme_success` - Success color background with appropriate text
- **Theme Accent**: `.theme_accent` - Accent color background with appropriate text

## Responsive Design

### Breakpoints
- **Small**: 640px and up
- **Medium**: 768px and up
- **Large**: 1024px and up
- **Extra Large**: 1280px and up
- **2x Extra Large**: 1536px and up

### Responsive Patterns
- Use Tailwind's responsive prefixes: `sm:`, `md:`, `lg:`, `xl:`, `2xl:`
- Mobile-first approach: Default styles for mobile, then enhance for larger screens
- Flexible layouts using grid and flexbox
- Appropriate font sizes and spacing for different screen sizes

## Icons
- Use `nuxt-icon` for consistent icon usage
- Standard icon sizes: 16px, 20px, 24px, 32px
- Match icon colors to text colors or use brand colors for emphasis

## Shadows and Elevation
- **Light Shadow**: `shadow-sm` - Subtle elevation
- **Medium Shadow**: `shadow` - Standard elevation
- **Large Shadow**: `shadow-lg` - Pronounced elevation
- **Extra Large Shadow**: `shadow-xl` - Maximum elevation
- **Dark Mode Shadows**: Use `dark:shadow-base_dark_300` for appropriate contrast

## Animation and Transitions
- **Duration**: 300ms for most transitions
- **Easing**: Ease-in-out for smooth transitions
- **Hover Effects**: Subtle scale or elevation changes
- **Page Transitions**: Fade transitions between pages

## Implementation Guidelines
1. Use Tailwind classes for most styling needs
2. Use custom classes (`.o_*`) for complex components
3. Maintain dark mode compatibility with `dark:` variants
4. Follow the mobile-first approach
5. Keep accessibility in mind (contrast, focus states, etc.)
6. Use theme classes consistently for background/text combinations

## Best Practices
1. Maintain consistent spacing using the spacing system
2. Use the defined color palette exclusively
3. Follow typography guidelines for readability
4. Ensure all components work in both light and dark modes
5. Test designs across all breakpoints
6. Use animations sparingly and purposefully
