# Firebase Migration Plan

## Overview

This document outlines the plan for migrating the Covalonic application to use centralized Firebase configurations from `@composables/useFirebase.ts` for the frontend and `@server/firebase/init.ts` for the backend. The goal is to eliminate all other Firebase initialization methods, including the Firebase plugin and direct imports from `config/firebase.ts`.

## Current State

The application currently has multiple Firebase initialization methods:

1. **Frontend Firebase Initialization**:
   - `config/firebase.ts` - Exports `firestoreDb` and initialization functions
   - `plugins/firebase.ts` - Nuxt plugin that initializes Firebase and provides it via `nuxtApp.$firebase`
   - Many components directly import `firestoreDb` from `config/firebase.ts`

2. **Backend Firebase Initialization**:
   - `server/lib/firebase/server-firebase.ts` - Initializes Firebase for server-side use
   - Server API routes import `firestoreDb` from this file

3. **New Centralized Configurations**:
   - `composables/useFirebase.ts` - For frontend Firebase usage
   - `server/firebase/init.ts` - For backend Firebase usage

## Migration Goals

1. Migrate all components and composables to use `useFirebase().firestore` instead of direct imports
2. Migrate all server API routes to use `useFirebaseServer()` instead of direct imports
3. Remove the Firebase plugin and its registration in `nuxt.config.ts`
4. Eventually remove `config/firebase.ts` entirely

## Migration Plan

### Phase 1: Preparation (1-2 days)

1. **Verify New Firebase Initialization Methods**:
   - Ensure `useFirebase.ts` is properly initializing Firebase for frontend
   - Ensure `server/firebase/init.ts` is properly initializing Firebase for backend
   - Add logging to track Firebase initialization and usage
   - Create a test component that uses the new initialization methods
   - Verify that the test component works correctly with Firebase

2. **Create a Backup**:
   - Create a backup branch of the current codebase
   - This will serve as a rollback point if needed

### Phase 2: Composables Migration (2-3 days)

1. **Update Composables**:
   - Update all composables to use `useFirebase().firestore` instead of direct imports
   - Start with these composables:
     - `composables/useAnalytics.ts`
     - `composables/useAdminAnalytics.ts`
     - `composables/useABTesting.ts`
     - `composables/analytics-tracking.ts`
     - `composables/useGeographicAnalytics.ts`
     - `composables/useEngagementFunnel.ts`
     - `composables/usePaymentProcessing.ts`
     - `composables/notifications.ts`
     - `composables/stats.ts`

2. **Testing**:
   - Test each composable after updating
   - Verify that Firebase operations still work correctly
   - Monitor for any Firebase-related errors

### Phase 3: Component Migration (3-4 days)

1. **Update Components**:
   - Update all components to use `useFirebase().firestore` instead of direct imports
   - Start with these components:
     - `components/flyers/app.vue`
     - `components/flyers/app-all.vue`
     - `components/flyers/app-home.vue`
     - `components/items/app.vue`
     - `components/items/app-all.vue`
     - `components/me/media.vue`
     - `components/me/accounts.vue`
     - `components/space/app.vue`
     - `components/space/app-all.vue`
     - `components/specials/app-all.vue`
     - `components/google/geo-list.vue`
     - `components/history/index.vue`
     - `components/covalonic/history.vue`
     - `components/icons/space.vue`

2. **Testing**:
   - Test each component after updating
   - Verify that Firebase operations still work correctly
   - Monitor for any Firebase-related errors

### Phase 4: Server-Side Migration (2-3 days)

1. **Update Server API Routes**:
   - Update all server API routes to use `useFirebaseServer()` instead of direct imports
   - Start with these files:
     - `server/api/analytics/track-duration.post.ts`
     - `server/api/firestore/query-order-by.ts`
     - `server/lib/firebase/auth.ts`
     - Other server API routes that use Firebase

2. **Testing**:
   - Test each API route after updating
   - Verify that Firebase operations still work correctly
   - Monitor for any Firebase-related errors

### Phase 5: Firebase Messaging Migration (1-2 days)

1. **Update Firebase Messaging**:
   - Modify `config/firebase-messaging.ts` to use `useFirebase()` directly
   - Update the `initializeMessaging()` function to get Firebase app from `useFirebase()`
   - Update other messaging functions to use `useFirebase()` directly

2. **Testing**:
   - Test messaging functionality
   - Verify that notifications work correctly
   - Monitor for any Firebase-related errors

### Phase 6: Remove Old Initialization (1-2 days)

1. **Update `composables/firebase.ts`**:
   - Modify `composables/firebase.ts` to use `useFirebase()` directly
   - Ensure all Firebase utility functions use the instances from `useFirebase()`

2. **Remove Firebase Plugin**:
   - Remove `plugins/firebase.ts`
   - Update `nuxt.config.ts` to remove the plugin registration

3. **Update `config/firebase.ts`**:
   - Update `config/firebase.ts` to export only deprecation warnings
   - Keep it temporarily for backward compatibility

4. **Testing**:
   - Test the entire application
   - Verify that all Firebase operations still work correctly
   - Monitor for any Firebase-related errors

### Phase 7: Final Cleanup (1 day)

1. **Remove `config/firebase.ts`**:
   - After thorough testing, remove `config/firebase.ts` entirely
   - Update any remaining imports or references

2. **Documentation**:
   - Document the new Firebase initialization methods
   - Update the project README with information about Firebase usage
   - Create examples for using Firebase in components, composables, and server API routes

3. **Final Testing**:
   - Perform final testing of the entire application
   - Verify that all Firebase operations work correctly
   - Monitor for any Firebase-related errors

## Implementation Details

### Component/Composable Update Pattern

For each file that directly imports `firestoreDb`:

1. Remove the import:
   ```typescript
   // Remove this line
   import { firestoreDb } from '@/config/firebase'
   ```

2. Add the useFirebase composable:
   ```typescript
   // Add this line
   const { firestore } = useFirebase()
   ```

3. Replace all instances of `firestoreDb` with `firestore`

### Server API Route Update Pattern

For each server API route:

1. Remove the import:
   ```typescript
   // Remove this line
   import { firestoreDb } from '../../lib/firebase/server-firebase'
   ```

2. Add the useFirebaseServer function:
   ```typescript
   // Add this line
   const { firestore } = await useFirebaseServer(event.context.auth?.token)
   ```

3. Replace all instances of `firestoreDb` with `firestore`

## Rollback Strategy

In case of critical issues:

1. **Rollback Triggers**:
   - Critical Firebase operations failing
   - Authentication flows broken
   - Performance degradation
   - Multiple Firebase initialization errors

2. **Rollback Steps**:
   - Revert to the backup branch
   - Test the application to ensure it's back to its original state

## Timeline

- **Total Duration**: 10-17 days
- **Phase 1**: 1-2 days
- **Phase 2**: 2-3 days
- **Phase 3**: 3-4 days
- **Phase 4**: 2-3 days
- **Phase 5**: 1-2 days
- **Phase 6**: 1-2 days
- **Phase 7**: 1 day

## Success Criteria

The migration will be considered successful when:

1. All components and composables use `useFirebase().firestore` instead of direct imports
2. All server API routes use `useFirebaseServer()` instead of direct imports
3. The Firebase plugin is removed and not registered in `nuxt.config.ts`
4. `config/firebase.ts` is removed entirely
5. All Firebase operations work correctly
6. No Firebase-related errors in the console
7. Performance is acceptable
