# Covalonic Form Style Guide

## Overview
This document provides comprehensive guidelines for form styling and behavior across the Covalonic platform. It ensures consistency in appearance, interaction, and validation feedback for all form elements.

## Form Elements

### Text Inputs

#### Styling
```html
<input 
  type="text"
  class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
  placeholder="Enter text"
/>
```

#### States
- **Default**: Gray border (`border-gray-300 dark:border-gray-700`)
- **Focus**: Blue border and ring (`focus:ring-[#0072ff] focus:border-[#0072ff]`)
- **Invalid**: Red border and ring (`border-red-500 focus:border-red-500 focus:ring-red-500`)
- **Valid**: Green border and ring (`border-green-500 focus:border-green-500 focus:ring-green-500`)
- **Disabled**: Gray background and reduced opacity (`bg-gray-100 dark:bg-gray-700 opacity-60 cursor-not-allowed`)

### Labels

#### Styling
```html
<label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
  Label Text
  <span v-if="required" class="text-red-500">*</span>
</label>
```

#### States
- **Default**: Dark gray text (`text-gray-700 dark:text-gray-300`)
- **Disabled**: Lighter gray text (`text-gray-500 dark:text-gray-500`)
- **Required**: Red asterisk (`<span class="text-red-500">*</span>`)

### Textareas

#### Styling
```html
<textarea 
  class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200"
  rows="4"
  placeholder="Enter text"
></textarea>
```

#### States
- Same as text inputs

### Select Dropdowns

#### Styling
```html
<select 
  class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-[#0072ff] focus:border-[#0072ff] dark:bg-gray-800 dark:text-gray-200 bg-white dark:bg-gray-800 appearance-none"
>
  <option value="" disabled selected>Select an option</option>
  <option value="option1">Option 1</option>
  <option value="option2">Option 2</option>
</select>
```

#### States
- Same as text inputs

### Checkboxes

#### Styling
```html
<div class="flex items-center">
  <input 
    type="checkbox" 
    class="w-4 h-4 text-[#0072ff] border-gray-300 rounded focus:ring-[#0072ff] dark:border-gray-700 dark:bg-gray-800"
  />
  <label class="ml-2 text-sm text-gray-700 dark:text-gray-300">
    Checkbox Label
  </label>
</div>
```

#### States
- **Default**: Gray border (`border-gray-300 dark:border-gray-700`)
- **Checked**: Blue background (`text-[#0072ff]`)
- **Focus**: Blue ring (`focus:ring-[#0072ff]`)
- **Disabled**: Gray background and reduced opacity (`opacity-60 cursor-not-allowed`)

### Radio Buttons

#### Styling
```html
<div class="flex items-center">
  <input 
    type="radio" 
    class="w-4 h-4 text-[#0072ff] border-gray-300 rounded-full focus:ring-[#0072ff] dark:border-gray-700 dark:bg-gray-800"
  />
  <label class="ml-2 text-sm text-gray-700 dark:text-gray-300">
    Radio Label
  </label>
</div>
```

#### States
- Same as checkboxes

### Buttons

#### Primary Button
```html
<button 
  class="px-4 py-2 text-sm font-medium text-white bg-[#0072ff] border border-transparent rounded-md shadow-sm hover:bg-[#0054bb] focus:outline-none focus:ring-2 focus:ring-[#0072ff] focus:ring-opacity-50"
>
  Submit
</button>
```

#### Secondary Button
```html
<button 
  class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-[#0072ff] focus:ring-opacity-50 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600"
>
  Cancel
</button>
```

#### States
- **Default**: As shown above
- **Hover**: Darker background (`hover:bg-[#0054bb]` for primary, `hover:bg-gray-50` for secondary)
- **Focus**: Blue ring (`focus:ring-2 focus:ring-[#0072ff] focus:ring-opacity-50`)
- **Disabled**: Reduced opacity (`opacity-60 cursor-not-allowed`)

### Validation Messages

#### Error Message
```html
<p class="mt-1 text-sm text-red-600 dark:text-red-400">
  Error message goes here
</p>
```

#### Success Message
```html
<p class="mt-1 text-sm text-green-600 dark:text-green-400">
  Success message goes here
</p>
```

#### Hint Message
```html
<p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
  Hint message goes here
</p>
```

## Form Layout

### Spacing
- **Between Form Elements**: 1rem (16px) - `mb-4`
- **Between Label and Input**: 0.25rem (4px) - `mb-1`
- **Between Input and Error Message**: 0.25rem (4px) - `mt-1`
- **Form Padding**: 1.5rem (24px) - `p-6`

### Grouping
- Group related fields together
- Use fieldsets for logical grouping
- Use dividers to separate different sections

```html
<fieldset class="mb-6">
  <legend class="text-base font-medium text-gray-900 dark:text-gray-100 mb-4">
    Section Title
  </legend>
  <!-- Form fields go here -->
</fieldset>
```

### Responsive Layout
- Use grid or flex layouts for form fields
- Stack fields vertically on mobile, side by side on larger screens

```html
<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
  <!-- Form fields go here -->
</div>
```

## Validation

### Inline Validation
- Validate on blur (when field loses focus)
- Show error messages below the field
- Use red border and text for errors
- Use green border for valid fields (optional)

### Form-Level Validation
- Validate all fields on form submission
- Prevent submission if any field is invalid
- Show a summary of errors at the top of the form (optional)
- Scroll to the first error field

## Accessibility

### Labels and ARIA
- Always use labels for form controls
- Use `aria-describedby` to associate error messages with inputs
- Use `aria-required="true"` for required fields
- Use `aria-invalid="true"` for invalid fields

### Focus Management
- Ensure all form controls are keyboard accessible
- Maintain visible focus states
- Set focus to the first error field after validation

## Implementation with Vue Components

### Base Input Component
```vue
<template>
  <div class="w-full">
    <label v-if="label" :for="id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
      {{ label }}
      <span v-if="required" class="text-red-500">*</span>
    </label>
    
    <div class="relative">
      <input
        :id="id"
        :type="type"
        :value="modelValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :required="required"
        :aria-required="required"
        :aria-invalid="!!error"
        :aria-describedby="error ? `${id}-error` : undefined"
        :class="[
          'w-full px-4 py-2 border rounded-md shadow-sm focus:outline-none',
          error ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : 'border-gray-300 dark:border-gray-700 focus:ring-[#0072ff] focus:border-[#0072ff]',
          isValid && (isTouched || isDirty) ? 'border-green-500' : '',
          disabled ? 'bg-gray-100 dark:bg-gray-700 opacity-60 cursor-not-allowed' : 'dark:bg-gray-800 dark:text-gray-200',
        ]"
        @input="$emit('update:modelValue', $event.target.value)"
        @blur="onBlur"
      />
    </div>
    
    <p v-if="error" :id="`${id}-error`" class="mt-1 text-sm text-red-600 dark:text-red-400">
      {{ error }}
    </p>
    <p v-else-if="hint" class="mt-1 text-sm text-gray-500 dark:text-gray-400">
      {{ hint }}
    </p>
  </div>
</template>
```

## Migration Strategy

1. Create new base components following this style guide
2. Update existing form components to use the new base components
3. Replace custom CSS classes (`o_input`, `o_label`, etc.) with the standardized Tailwind classes
4. Ensure all components support validation and accessibility
5. Test in both light and dark modes
