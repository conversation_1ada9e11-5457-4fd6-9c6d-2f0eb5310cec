# Task Log: Blogs List Redesign Plan

## Task Information
- **Date**: 2025-05-15
- **Time Started**: 13:15
- **Time Completed**: 13:30
- **Files Modified**: None (Planning phase)

## Task Details
- **Goal**: Create a detailed plan for redesigning `@pages/c/blogs/list.vue` to match the look and feel of `@pages/c/flyers/list.vue`.
- **Implementation**: 
  - Analyzed the current implementation of blogs list page
  - Compared it with the flyers list page to identify differences
  - Created a detailed plan for the redesign based on UI guidelines
- **Challenges**: 
  - The current blogs list page is very minimal and uses a component
  - Need to ensure proper data fetching and display
  - Need to maintain functionality while updating the UI
- **Decisions**: 
  - Will restructure the page to match the flyers list page layout
  - Will implement similar search and filter functionality
  - Will ensure consistent styling with the flyers list page

## Performance Evaluation
- **Score**: 21/23
- **Strengths**: 
  - Comprehensive analysis of both pages
  - Clear plan for implementing the redesign
  - Detailed component-by-component approach
  - Consideration of both functionality and styling
- **Areas for Improvement**: 
  - Could include more specific examples of the data structure needed

## Next Steps
- Implement the redesign of the blogs list page according to the plan
- Test the changes to ensure they meet the success criteria
- Document the changes in a new task log

## Detailed Implementation Plan

### Current State Analysis

1. **pages/c/blogs/list.vue**
   - Very minimal implementation
   - Simply includes the blogs-list component
   - Uses "theme_200" class for styling

2. **components/blogs/list.vue**
   - Main implementation of the blogs list
   - Uses layouts-main component for structure
   - Displays blogs in a grid with background images
   - Has a create blog button
   - Fetches data using queryByWhere2

### Target State (Based on flyers/list.vue)

1. **pages/c/flyers/list.vue**
   - Self-contained implementation (doesn't rely on external components)
   - Has a header section with title and description
   - Includes search and filter functionality
   - Displays flyers in a grid with consistent card styling
   - Has an "Add New" button
   - Includes an empty state for when no flyers are found

### Implementation Steps

1. **Restructure pages/c/blogs/list.vue**
   - Move the implementation from components/blogs/list.vue into the page
   - Follow the structure of flyers/list.vue
   - Include header, search, and grid sections

2. **Update Data Fetching**
   - Keep the existing data fetching logic from blogs/list.vue
   - Add computed property for filtering blogs based on search input

3. **Add Header Section**
   - Add a title "Blogs" with proper typography
   - Add a description "Browse and manage your blogs collection"
   - Use consistent styling with flyers/list.vue

4. **Add Search and Filter Section**
   - Add a search input for filtering blogs
   - Add an "Add New" button that navigates to blog creation
   - Use consistent styling with flyers/list.vue

5. **Update Blogs Grid**
   - Keep the existing grid layout but update styling
   - Ensure consistent card styling with flyers/list.vue
   - Maintain the background image display
   - Update hover and focus states

6. **Add Empty State**
   - Add an empty state for when no blogs are found
   - Include a message and an "Add New Blog" button
   - Use consistent styling with flyers/list.vue

### Code Structure

```vue
<script setup lang="ts">
import { ref, computed } from 'vue';
import { useState, useRouter } from '#app';

definePageMeta({
  layout: "covalonic",
});

const { blogsContent, blogsContentSelected } = useContent();
const { currentSpace } = space();
const filter = ref('');

// Fetch blogs data
const getData = async () => {
  const data = await queryByWhere2(
    "blogs-content",
    "spaces",
    currentSpace.value.id,
    "array-contains"
  );
  blogsContent.value = data.result;
};

getData();

// Filter blogs based on search input
const blogsFilter = computed(() => {
  return blogsContent.value.filter((item: any) => 
    item.title?.toLowerCase().includes(filter.value.toLowerCase())
  );
});

// Select blog for editing
const selectBlog = (blog: any) => {
  blogsContentSelected.value = blog;
  useRouter().push(`/c/blog-detail?id=${blog.id}`);
};
</script>

<template>
  <div class="h-full min-h-screen pb-12 font-sans md:p-4 bg-white dark:bg-gray-800">
    <!-- Header Section -->
    <div class="mb-6">
      <h1 class="text-2.5rem font-bold text-gray-800 dark:text-white mb-2">Blogs</h1>
      <p class="text-base text-gray-600 dark:text-gray-300">Browse and manage your blogs collection</p>
    </div>

    <!-- Search and Filter Section -->
    <div class="flex w-full mb-6 bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
      <!-- Search Input -->
      <!-- Add New Button -->
    </div>

    <!-- Blogs Grid -->
    <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      <!-- Blog Cards -->
    </div>

    <!-- Empty State -->
    <div v-if="blogsFilter.length === 0" class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-8 text-center mt-6">
      <!-- Empty State Content -->
    </div>
  </div>
</template>
```

### Styling Updates

1. **Color Scheme**
   - Use the brand colors from UI guidelines
   - Primary Blue: `#0072ff` for buttons and accents
   - Ensure proper dark mode support

2. **Typography**
   - Update font sizes according to the UI guidelines
   - Heading 1: 2.5rem (40px) for page title
   - Body: 1rem (16px) for regular text

3. **Component Styling**
   - Cards: Apply consistent shadow, border-radius, and padding
   - Buttons: Update to match the button styles in UI guidelines
   - Search input: Style consistently with other search inputs

4. **Responsive Behavior**
   - Ensure proper grid layout on different screen sizes
   - Use responsive classes for proper spacing and alignment

### Testing Plan

1. Test data fetching and display
2. Test search functionality
3. Test navigation to blog creation and editing
4. Test empty state display
5. Test responsive behavior on different screen sizes
6. Test in both light and dark modes
