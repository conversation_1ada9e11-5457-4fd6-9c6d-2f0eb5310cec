# Technology Context

**Frontend:**
- **Framework**: Nuxt 3 (Vue.js-based framework)
- **Language**: TypeScript/JavaScript
- **Styling**: TailwindCSS
- **UI Components**: Custom components with Tailwind styling

**Backend:**
- **Framework**: Firebase Cloud Functions (Node.js)
- **Language**: JavaScript
- **API**: RESTful endpoints via Nuxt server routes

**Database:**
- **Type**: Firebase Firestore (NoSQL)
- **Authentication**: Firebase Authentication
- **Storage**: Firebase Storage (for business card images)
- **Firebase Configuration**:
  - Frontend: Centralized via `composables/useFirebase.ts`
  - Backend: Centralized via `server/firebase/init.ts`

**Deployment:**
- **Platform**: Firebase Hosting
- **CI/CD**: Manual deployment via Firebase CLI
- **Containerization**: Docker support available (see Dockerfile)

**Key Dependencies:**
- **PWA**: @vite-pwa/nuxt for Progressive Web App functionality
- **OCR**: Tesseract.js for business card text recognition
- **Maps**: Vue Google Maps Community Fork for location features
- **Charts**: Chart.js for analytics visualization
- **Forms**: Custom form components
- **VCard**: vcards-js for generating digital business cards
- **QR Codes**: @vueuse/integrations/useQRCode for QR code generation
- **Push Notifications**: Firebase Cloud Messaging (FCM)

**Development Tools:**
- **Package Manager**: npm/pnpm
- **Build Tool**: Vite (via Nuxt)
- **Version Control**: Git
- **Linting/Formatting**: ESLint/Prettier
